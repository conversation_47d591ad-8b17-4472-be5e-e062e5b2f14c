from bs4 import BeautifulSoup

def format_element(elem, indent=0):
    INDENT = "  " * indent
    lines = []

    # 显示标签 + 类名/id（如果有）
    tag_info = elem.name
    if elem.get('id'):
        tag_info += f"#{elem['id']}"
    if elem.get('class'):
        tag_info += f".{'/'.join(elem['class'])}"

    # 显示文本
    text = elem.get_text(strip=True)
    if text:
        lines.append(f"{INDENT}<{tag_info}>: {text}")
    else:
        lines.append(f"{INDENT}<{tag_info}>")

    # 递归处理子节点
    for child in elem.find_all(recursive=False):
        if child.name:
            lines.append(format_element(child, indent + 1))

    return "\n".join(lines)

def extract_html_structure(html):
    soup = BeautifulSoup(html, 'html.parser')

    # 去掉无意义标签
    for tag in soup(['script', 'style', 'meta', 'link', 'noscript']):
        tag.decompose()

    body = soup.body
    if not body:
        return "未找到 <body> 标签"

    output = []
    for child in body.find_all(recursive=False):
        if child.name:
            output.append(format_element(child, indent=0))

    return "\n\n".join(output)

if __name__ == "__main__":
    html_path = input("请输入 HTML 文件路径（例如 index.html）：").strip()
    try:
        with open(html_path, 'r', encoding='utf-8') as f:
            html = f.read()

        result = extract_html_structure(html)

        output_path = "output_structured.txt"
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(result)

        print(f"\n✅ 提取完成！结构已保存到 {output_path}，适合丢给大模型自动化分析。\n")

    except Exception as e:
        print(f"❌ 读取失败：{e}")
