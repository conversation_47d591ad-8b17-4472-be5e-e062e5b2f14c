import time
import os
import random
import requests
import json
from datetime import datetime
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from bit_api import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from urllib.parse import urlparse
import re
import concurrent.futures
import pyperclip
import tkinter as tk
from tkinter import ttk
import paramiko


APP_KEY = "f08bf7f7cfe8c038"
SIGN = "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ=="
JZTJ_WORKSHEET_ID = "jztj"
OWNER_ID = "b6c64060-3fbb-44c5-87ee-52c5f04e50af"
BJK_WORKSHEET_ID = "jzbjk"

# 企业微信通知配置
# WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=073c8357-e2b2-45cb-b0bd-e04ce27e3e61"
# WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77e0cc90-98f3-4b03-a4f3-9ee335c14c07"
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b9475b2e-4c9a-42a5-abbd-5072ea1fea08"

def check_login_status(driver):
    """检测登录状态 - 检查是否有login-container类"""
    try:
        # 检查是否有登录容器
        login_containers = driver.find_elements(By.CLASS_NAME, "login-container")
        if login_containers:
            for container in login_containers:
                if container.is_displayed():
                    return False  # 发现登录页面，表示未登录
        return True  # 没有发现登录页面，表示已登录
    except Exception as e:
        print(f"⚠️ 检测登录状态失败: {e}")
        return True  # 默认认为已登录，避免误判

def check_qrcode_verification(driver):
    """检测二维码验证 - 检查是否有二维码验证弹窗"""
    try:
        # 检查二维码验证相关元素
        qrcode_selectors = [
            ".static-content .qrcode-desc",
            ".qrcode-container",
            ".qrcode-img",
            "[class*='qrcode']"
        ]

        for selector in qrcode_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        # 进一步检查是否包含特定文本
                        if "小红书App" in element.text or "扫一扫" in element.text or "扫码验证" in element.text:
                            return True
                        # 或者检查是否是二维码图片
                        if "qrcode-img" in element.get_attribute("class"):
                            return True
            except:
                continue

        return False  # 没有发现二维码验证
    except Exception as e:
        print(f"⚠️ 检测二维码验证失败: {e}")
        return False  # 默认认为没有验证，避免误判



def send_to_wechat_group(webhook_url: str, message: str, userid: str = None):
    """发送企业微信群通知"""
    # 如果有 userid，前面加 @
    if userid:
        content = f"<@{userid}>\n{message}"
    else:
        content = message

    payload = {
        "msgtype": "markdown",
        "markdown": {
            "content": content
        }
    }

    try:
        response = requests.post(webhook_url, json=payload)
        if response.status_code == 200:
            print("✅ 企业微信通知发送成功")
        else:
            print(f"❌ 企业微信通知发送失败，状态码：{response.status_code}，响应内容：{response.text}")
    except Exception as e:
        print(f"❌ 发送企业微信通知时出错：{e}")


def fetch_qw_phone(monitor_xhs_number: str) -> str:
    url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    payload = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "account_config",
        "filters": [
            {
                "controlId": "id",
                "dataType": 2,
                "spliceType": 1,
                "filterType": 1,
                "value": monitor_xhs_number
            }
        ]
    }

    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        data = response.json()
        if data.get("success") and data.get("data", {}).get("rows"):
            row = data["data"]["rows"][0]
            return row.get("qw_phone", "")  # 只返回手机号
    except Exception as e:
        print(f"获取企业微信手机号失败：{e}")
    return ""

def handle_account_exception(browser_id, browser_name, xhs_account, exception_type="掉登录", auto_action="已自动关闭浏览器"):
    """处理账号异常情况"""
    print(f"⚠️ 检测到浏览器 {browser_id} ({browser_name}) {exception_type}")

    # 关闭浏览器
    try:
        closeBrowser(browser_id)
        print(f"✅ 浏览器 {browser_id} 已关闭")
    except Exception as e:
        print(f"⚠️ 关闭浏览器失败: {e}")

    # 根据异常类型设置不同的图标和标题
    exception_icons = {
        "掉登录": "🔐",
        "账号异常": "⚠️",
        "验证码": "🚨",
        "二维码验证": "📱",
        "封号": "🚫",
        "限流": "⏳"
    }

    icon = exception_icons.get(exception_type, "⚠️")

    # 发送企业微信通知
    message = (
        f"### {icon} 小红书账号异常提醒\n"
        f"- **异常类型**: {exception_type}\n"
        f"- **窗口ID**: {browser_id}\n"
        f"- **窗口名称**: {browser_name}\n"
        f"- **小红书账号**: {xhs_account}\n"
        f"- **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        # f"- **处理状态**: {auto_action}\n\n"
        f"占用线程资源，请及时处理！"
    )

    userid = fetch_qw_phone(xhs_account)
    send_to_wechat_group(WECHAT_WEBHOOK_URL, message, userid)
    return False  # 返回False表示需要停止当前操作

def handle_login_logout(browser_id, browser_name, xhs_account):
    """处理掉登录情况 - 兼容性函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "掉登录", "已自动关闭浏览器")

def handle_captcha_detected(browser_id, browser_name, xhs_account):
    """处理验证码检测情况 - 预留函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "验证码", "已暂停操作，等待手动处理")

def handle_account_banned(browser_id, browser_name, xhs_account):
    """处理账号封禁情况 - 预留函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "封号", "已自动关闭浏览器")

def handle_account_limited(browser_id, browser_name, xhs_account):
    """处理账号限流情况 - 预留函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "限流", "已自动关闭浏览器")

def handle_general_exception(browser_id, browser_name, xhs_account):
    """处理一般账号异常情况 - 预留函数"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "账号异常", "已自动关闭浏览器")

def handle_qrcode_verification(browser_id, browser_name, xhs_account):
    """处理二维码验证情况"""
    return handle_account_exception(browser_id, browser_name, xhs_account, "二维码验证", "已自动关闭浏览器")

# ============================================================================
# 账号异常处理使用说明：
#
# 1. 掉登录：    handle_login_logout(browser_id, browser_name, xhs_account)
# 2. 验证码：    handle_captcha_detected(browser_id, browser_name, xhs_account)
# 3. 二维码验证：handle_qrcode_verification(browser_id, browser_name, xhs_account)
# 4. 封号：      handle_account_banned(browser_id, browser_name, xhs_account)
# 5. 限流：      handle_account_limited(browser_id, browser_name, xhs_account)
# 6. 其他：      handle_general_exception(browser_id, browser_name, xhs_account)
#
# 或者直接使用通用函数：
# handle_account_exception(browser_id, browser_name, xhs_account, "自定义异常类型", "自定义处理状态")
#
# 二维码验证检测：
# - 主要在养号流程中点击笔记后检测
# - 检测元素：.qrcode-desc, .qrcode-container, .qrcode-img 等
# - 检测文本：包含"小红书App"、"扫一扫"、"扫码验证"等关键词
# ============================================================================

def parse_count(text):
    try:
        if not text:
            return 0
        text = text.replace(",", "").strip().lower()  # 去千位符、空格、小写化
        if 'w' in text:
            return int(float(text.replace('w', '')) * 10000)
        if '万' in text:
            return int(float(text.replace('万', '')) * 10000)
        return int(float(text))  # 防止 '12.0' 类型报错
    except:
        return 0

    
def account_matrix(driver):

    try:

        try:
            username_element = WebDriverWait(driver, 180).until(EC.presence_of_element_located((By.CSS_SELECTOR, ".account-name")))
            username = username_element.text.strip()
        except Exception as e:
            username = None
            print("未找到昵称", e)

        # 提取小红书号
        try:
            xhs_id_element = WebDriverWait(driver, 180).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(text(), '小红书账号')]")
                )
            )
            xhs_number = xhs_id_element.text.split(":")[-1].strip()  # 英文冒号
        except Exception as e:
            xhs_number = None
            print("未找到小红书号", e)

        # 如果两个都没获取到，直接返回，不执行后面逻辑
        if not username or not xhs_number:
            print("❌ 昵称或小红书号缺失，跳过该账号")
            return    

        # 提取关注、粉丝、获赞与收藏
        # 等待主容器加载
        stats_container = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.description-text")))

        # 抓取三个数值
        elements = stats_container.find_elements(By.CSS_SELECTOR, "span.numerical")
        if len(elements) >= 3:
            following_count = elements[0].text
            follower_count = elements[1].text
            like_fav_count = elements[2].text
        else:
            print("未能成功提取到所有数据")

        # 打印信息
        # print(f"小红书号：{xhs_number}")
        # print(f"昵称：{username}")
        # print(f"关注：{following_count}")
        # print(f"粉丝：{follower_count}")
        # print(f"获赞与收藏：{like_fav_count}")

        # 查询是否已有记录
        search_url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
        filter_payload = {
            "appKey": APP_KEY,
            "sign": SIGN,
            "worksheetId": JZTJ_WORKSHEET_ID,
            "pageSize": 1,
            "pageIndex": 1,
            "filters": [
                {
                    "controlId": "xhsh",
                    "dataType": 2,
                    "spliceType": 1,
                    "filterType": 3,
                    "value": xhs_number
                }
            ]
        }
        res = requests.post(search_url, json=filter_payload)
        result = res.json()

        if result.get("data", {}).get("rows"):
            # 已存在，走修改接口
            row_id = result["data"]["rows"][0]["rowid"]
            edit_url = "https://api.mingdao.com/v2/open/worksheet/editRow"
            edit_payload = {
                "appKey": APP_KEY,
                "sign": SIGN,
                "worksheetId": JZTJ_WORKSHEET_ID,
                "rowId": row_id,
                "triggerWorkflow": True,
                "controls": [
                    {"controlId": "zhmc", "value": username},
                    {"controlId": "xhsh", "value": xhs_number},
                    {"controlId": "guanzhu", "value": following_count},
                    {"controlId": "fensi", "value": follower_count},
                    {"controlId": "hzysc", "value": like_fav_count},
                    {"controlId": "guanzhu_count", "value": parse_count(following_count)},
                    {"controlId": "fensi_count", "value": parse_count(follower_count)},
                    {"controlId": "hzysc_count", "value": parse_count(like_fav_count)}
                ]
            }
            res = requests.post(edit_url, json=edit_payload)
            print("✅ 修改成功矩阵管理" if res.status_code == 200 else f"❌ 修改失败: {res.text}")
        else:
            # 不存在，走新增接口
            add_url = "https://api.mingdao.com/v2/open/worksheet/addRow"
            add_payload = {
                "appKey": APP_KEY,
                "sign": SIGN,
                "worksheetId": JZTJ_WORKSHEET_ID,
                "triggerWorkflow": True,
                "controls": [
                    {"controlId": "zhmc", "value": username},
                    {"controlId": "xhsh", "value": xhs_number},
                    {"controlId": "guanzhu", "value": following_count},
                    {"controlId": "fensi", "value": follower_count},
                    {"controlId": "hzysc", "value": like_fav_count},
                    {"controlId": "guanzhu_count", "value": parse_count(following_count)},
                    {"controlId": "fensi_count", "value": parse_count(follower_count)},
                    {"controlId": "hzysc_count", "value": parse_count(like_fav_count)}
                ]
            }
            res = requests.post(add_url, json=add_payload)
            print("✅ 新增成功矩阵管理" if res.status_code == 200 else f"❌ 新增失败: {res.text}")

        return xhs_number, username

    except Exception as e:
        print(f"获取账号信息失败：{e}")
        return None, None


# 抓取笔记信息
def scrape_notes(driver, xhs_number, nickname):
    print("开始抓取笔记信息")
    try:
        WebDriverWait(driver, 180).until(
            EC.presence_of_all_elements_located((By.CLASS_NAME, "note"))
        )
    except Exception as e:
        print("❌ 加载 note 元素超时：", e)
        return

    notes = driver.find_elements(By.CLASS_NAME, "note")

    for note in notes:
        try:
            title = note.find_element(By.CLASS_NAME, "title").text.strip()
            time = note.find_element(By.CLASS_NAME, "time").text.strip()

            icons = note.find_elements(By.CLASS_NAME, "icon")
            icon_values = [icon.text.strip() for icon in icons]

            view_count = icon_values[0] if len(icon_values) > 0 else "0"
            comment_count = icon_values[1] if len(icon_values) > 1 else "0"
            like_count = icon_values[2] if len(icon_values) > 2 else "0"
            favorite_count = icon_values[3] if len(icon_values) > 3 else "0"
            share_count = icon_values[4] if len(icon_values) > 4 else "0"


            # 提取封面图链接
            cover_img_elem = note.find_element(By.CSS_SELECTOR, ".media-bg")
            cover_style = cover_img_elem.get_attribute("style")
            match = re.search(r'url\("([^"]+)"\)', cover_style)
            cover_img_url = match.group(1) if match else ""

            # ✅ 先查询是否已存在这条笔记（根据 xhs_number + title）
            check_payload = {
                "appKey": APP_KEY,
                "sign": SIGN,
                "worksheetId": BJK_WORKSHEET_ID,
                "pageSize": 100,
                "pageIndex": 1,
                "filters": [
                    {"controlId": "xhsh", "dataType": 2, "spliceType": 1, "filterType": 2,"value": xhs_number},
                    {"controlId": "bjbt", "dataType": 2, "spliceType": 1, "filterType": 2,"value": title}
                ]
            }

            check_res = requests.post("https://api.mingdao.com/v2/open/worksheet/getFilterRows", json=check_payload)
            row_id = None
            if check_res.status_code == 200:
                rows = check_res.json().get("data", {}).get("rows", [])
                if rows:
                    row_id = rows[0]["rowid"]

            # 构建字段
            controls = [
                {"controlId": "bjbt", "value": title},
                {"controlId": "xiaoyanjing", "value": view_count},
                {"controlId": "pinglun", "value": comment_count},
                {"controlId": "dianzan", "value": like_count},
                {"controlId": "shoucang", "value": favorite_count},
                {"controlId": "zhuanfa", "value": share_count},
                {"controlId": "xiaoyanjing_number", "value": parse_count(view_count)},
                {"controlId": "pinglun_number", "value": parse_count(comment_count)},
                {"controlId": "dianzan_number", "value": parse_count(like_count)},
                {"controlId": "shoucang_number", "value": parse_count(favorite_count)},
                {"controlId": "zhuanfa_number", "value": parse_count(share_count)},
                {"controlId": "xhsh", "value": xhs_number},
                {"controlId": "nicheng", "value": nickname},
                {"controlId": "fengmian", "value": cover_img_url}
            ]
            if row_id:
                # ✅ 更新笔记
                payload = {
                    "appKey": APP_KEY,
                    "sign": SIGN,
                    "worksheetId": BJK_WORKSHEET_ID,
                    "rowId": row_id,
                    "triggerWorkflow": True,
                    "controls": controls
                }
                res = requests.post("https://api.mingdao.com/v2/open/worksheet/editRow", json=payload)
                print(f"📝 已更新：{title}" if res.status_code == 200 else f"❌ 更新失败：{title}，响应：{res.text}")
            else:
                # ✅ 新增笔记
                payload = {
                    "appKey": APP_KEY,
                    "sign": SIGN,
                    "worksheetId": BJK_WORKSHEET_ID,
                    "triggerWorkflow": True,
                    "controls": controls
                }
                res = requests.post("https://api.mingdao.com/v2/open/worksheet/addRow", json=payload)
                print(f"✅ 已新增：{title}" if res.status_code == 200 else f"❌ 新增失败：{title}，响应：{res.text}")

        except Exception as e:
            print("⚠️ 解析某条笔记失败：", e)


def open_browser(window_id):
    """调用 openBrowser 方法获取 Selenium 连接信息"""
    return openBrowser(window_id)  # 直接调用已有的 openBrowser 方法

def get_account_number(driver):
    """获取当前登录的小红书账号"""
    driver.get('https://www.xiaohongshu.com/explore')
    my_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.XPATH, '//span[text()="我"]'))
    )
    my_button.click()
    
    # red_id_element = driver.find_element(By.XPATH, '//span[@class="user-redId"]')
    red_id_element = WebDriverWait(driver, 10).until(
        EC.visibility_of_element_located((By.XPATH, '//span[@class="user-redId"]'))
    )
    red_id = red_id_element.text.split("：")[1]
    print(f"当前小红书账号: {red_id}")
    return red_id

def check_fabu_config(browser):
    """检查是否有符合条件的发布内容"""
    full_remark = browser.get("remark", "")
    account_number = full_remark.split("_")[0].strip()
    # print(f"xxxxxxxxxxx:{account_number}")
    name = browser.get("name", "")
    print(f"检查 {name} 的待发布内容...")
    time.sleep(random.randint(5, 10))
    url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    headers = {"Content-Type": "application/json"}
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "jzzhnrff",
        "pageSize": 1000,
        "pageIndex": 1,
        "controls": [],
        "filters": [
            {"controlId": "release_status", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "未发布"},
            {"controlId": "channel_type", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "小红书"}
        ]
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            res = response.json()
            if res.get("success") and "rows" in res.get("data", {}):
                rows = res["data"]["rows"]
                for row in rows:
                    account_data_str = row.get("account", "")
                    release_time = row.get("release_time")
                    account_data = json.loads(account_data_str)
                    if isinstance(account_data, list):
                        for account_item in account_data:
                            source_value = json.loads(account_item.get("sourcevalue", "{}"))
                            account_in_data = source_value.get("66d7fffe98435d4ec600ca08", "")
                            if account_in_data.strip().lower() == account_number.strip().lower():
                                release_time_obj = datetime.strptime(release_time, "%Y-%m-%d %H:%M:%S")
                                if datetime.now() >= release_time_obj:
                                    return row  # 直接返回符合条件的发布记录
    except Exception as e:
        print(f"请求出错: {e}")
    return None

def clean_filename(filename):
    """
    清理文件名中的非法字符（包括 ?、=、&、: 等），确保在 Windows 操作系统中合法。
    :param filename: 原始文件名
    :return: 清理后的合法文件名
    """
    return re.sub(r'[\\/*?:"<>|]', "_", filename)

def get_filename_from_url(url):
    """
    从 URL 中提取文件名，并去掉查询参数部分。
    :param url: 文件的下载 URL
    :return: 清理后的文件名
    """
    parsed_url = urlparse(url)
    file_name = os.path.basename(parsed_url.path)  # 提取 URL 中的文件名部分
    return file_name.split('?')[0]  # 去掉查询参数部分

def download_media(tp_sp):
    """下载图片或视频并返回本地路径列表"""
    if isinstance(tp_sp, str):
        tp_sp = json.loads(tp_sp)

    if not isinstance(tp_sp, list) or not tp_sp:
        print("未找到有效的 tp_sp 数据")
        raise ValueError("未找到有效的 tp_sp 数据")
    
    media_paths = []
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    fastdown_folder = os.path.join(desktop_path, "fastdown")
    os.makedirs(fastdown_folder, exist_ok=True)
    
    for media_info in tp_sp:
        download_url = media_info.get("DownloadUrl")
        if not download_url:
            print("未找到有效的文件下载 URL")
            raise ValueError("未找到有效的文件下载 URL")
        
        response = requests.get(download_url, stream=True, timeout=30)
        if response.status_code != 200:
            raise Exception(f"文件下载失败，状态码: {response.status_code}")
        
        file_name = clean_filename(get_filename_from_url(download_url))
        local_file_path = os.path.join(fastdown_folder, file_name)
        
        with open(local_file_path, "wb") as file:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    file.write(chunk)
        
        print(f"文件已保存到本地路径: {local_file_path}")
        media_paths.append(local_file_path)
    
    return media_paths

def get_media_type(file_name):
    """
    根据文件扩展名判断文件类型（图片或视频）。
    :param file_name: 文件名
    :return: 媒体类型 ('image' 或 'video')
    """
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
    
    _, ext = os.path.splitext(file_name.lower())
    
    if ext in image_extensions:
        return 'image'
    elif ext in video_extensions:
        return 'video'
    else:
        return 'unknown'  # 如果是未知类型
    

def run_automation(driver, row, browser_id, xhs_account=None, browser_name=None):
    """执行小红书自动化发布（支持图文 & 视频 + 视频封面）"""

    title = row.get("title", "默认标题")
    zhengwen = row.get("zhengwen", "默认正文")
    tp_sp = row.get("tp_sp", [])  # 可能包含图片或视频
    video_cover = row.get("video_cover", [])  # 视频封面（可选）

    if isinstance(video_cover, str):
        try:
            video_cover = json.loads(video_cover)  # 解析 JSON 字符串
        except json.JSONDecodeError:
            video_cover = []  # 解析失败就置为空列表
    try:
        media_paths = download_media(tp_sp)  # 下载所有图片 & 视频
        if video_cover:
            print("进入封面下载")
            cover_path = download_media(video_cover)
    except Exception as e:
        print(f"媒体下载失败: {e}")
        return

    print("执行自动化发布...")
    driver.get('https://creator.xiaohongshu.com/publish/publish?source=official')

    # 访问小红书页面后立即检测登录状态
    time.sleep(3)  # 等待页面加载
    if not check_login_status(driver):
        print(f"🔐 访问发布页面后检测到浏览器 {browser_id} 未登录，停止发布操作")
        name = browser_name or f"浏览器_{browser_id}"
        account_name = xhs_account or "未知账号"
        handle_login_logout(browser_id, name, account_name)
        return

    if not media_paths:
        print("未找到媒体文件，取消发布")
        return

    # **判断是否有视频**
    has_video = any(get_media_type(path) == "video" for path in media_paths)

    if has_video:
        # **处理视频发布**
        # 定位到 "上传视频" 标签
        upload_video_tab = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//*[text()='上传视频']"))
        )
        upload_video_tab.click()
        time.sleep(2) 

        upload_btn = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, '//div[contains(@class, "creator-tab")]/span[text()="上传视频"]'))
        )
        upload_btn.click()

        # 只上传第一个视频
        video_path = next(path for path in media_paths if get_media_type(path) == "video")
        upload_input = driver.find_element(By.XPATH, '//input[@type="file" and contains(@class, "upload-input")]')
        upload_input.send_keys(video_path)
        time.sleep(8)

        # 等待“视频解析中”进度条消失
        WebDriverWait(driver, 600).until(
            EC.invisibility_of_element_located((By.XPATH, '//div[contains(text(), "视频解析中")]'))
        )
        print("视频解析完成")

        # 等待“上传中”进度条消失
        WebDriverWait(driver, 600).until(
            EC.invisibility_of_element_located((By.XPATH, '//div[contains(text(), "上传中")]'))
        )
        print("视频上传完成")

        """处理封面上传"""
        try:
            # 上传封面文件
            if cover_path:
                print(cover_path)
                # 点击“设置封面”按钮
                set_cover_btn = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//div[contains(@class, "uploadCover")]'))
                )
                set_cover_btn.click()
                time.sleep(2)
                # 等待 file input 出现
                upload_input = WebDriverWait(driver, 300).until(
                    EC.presence_of_element_located((
                        By.XPATH, '//input[@type="file" and @accept="image/png, image/jpeg, image/*"]'
                    ))
                )

                # 直接传入文件路径上传
                path_to_upload = cover_path[0].replace("\\", "\\\\")
                upload_input.send_keys(path_to_upload)

                time.sleep(3)
                # 等待弹窗出现
                modal = WebDriverWait(driver, 120).until(
                    EC.visibility_of_element_located((By.CLASS_NAME, "d-modal"))
                )

                # 在弹窗里等待“确定”按钮可点击
                confirm_button = WebDriverWait(modal, 120).until(
                    EC.element_to_be_clickable((By.XPATH, ".//button[contains(@class, 'mojito-button') and contains(., '确定')]"))
                )

                confirm_button.click()

                # 监控 `loading` 元素的 `style`，等待它变成 `display: none;`，说明封面上传成功
                WebDriverWait(driver, 120).until(
                    lambda d: 'display: none' in d.find_element(By.XPATH, '//div[contains(@class, "loading")]').get_attribute("style")
                )

                # 等待弹窗彻底关闭
                WebDriverWait(driver, 120).until(
                    EC.invisibility_of_element_located((By.CLASS_NAME, "d-modal"))
                )
                print("弹窗已关闭！")
                print("封面上传完成！")
            else:
                print("封面路径为空，跳过上传")

        except Exception as e:
            print(f"封面选择失败: {e}")

    else:
        # **处理图文发布**
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '.creator-tab'))
        )
        tabs = driver.find_elements(By.CSS_SELECTOR, '.creator-tab')

        for i, tab in enumerate(tabs):
            try:
                title_el = tab.find_element(By.CSS_SELECTOR, 'span.title')
                title_btn = title_el.text.strip()

                if title_btn == "上传图文" and tab.is_displayed():
                    tab.click()
                    break
            except Exception as e:
                print(f"点击第 {i+1} 个上传图文时异常：{e}")

        upload_input = driver.find_element(By.XPATH, '//input[@type="file" and contains(@class, "upload-input")]')
        upload_input.send_keys("\n".join(media_paths))  # 上传多张图片
        time.sleep(8)

        # 等待所有进度条消失
        WebDriverWait(driver, 600).until(
            EC.invisibility_of_element_located((By.XPATH, '//div[contains(@class, "center")]/div[contains(@class, "inner")]'))
        )

        # 依次等待每个上传的进度条消失
        # for i in range(len(media_paths)):
        #     WebDriverWait(driver, 60).until(
        #         EC.invisibility_of_element_located((By.XPATH, '//div[contains(@class, "center")]/div[contains(@class, "inner")]'))
        #     )
        #     print(f"第 {i+1} 张图片上传完成")

        print("所有图片上传完成，继续执行后续流程")
        time.sleep(2)

    # **填写标题、正文**
    time.sleep(2)
    # 复制标题到剪贴板
    pyperclip.copy(title)
    title_input = WebDriverWait(driver, 200).until(EC.presence_of_element_located((By.XPATH, '//input[@class="d-text" and @type="text"]')))
    driver.execute_script("arguments[0].scrollIntoView();", title_input)
    # title_input.send_keys(title)
    title_input.click()  # 确保输入框获得焦点
    title_input.send_keys(Keys.CONTROL, 'v')

    # 输入正文
    # 复制文本到剪贴板
    time.sleep(2)
    pyperclip.copy(zhengwen)
    editor_input = driver.find_element(By.XPATH, '//div[@class="ql-editor ql-blank"]')
    # editor_input.send_keys(zhengwen)
    # 定位输入框并粘贴
    editor_input.click()  # 确保输入框获得焦点
    editor_input.send_keys(Keys.CONTROL, 'v')  # Windows / Linux
    time.sleep(3)

    editor_input.send_keys(Keys.ENTER)
    time.sleep(2)

    # **处理话题**
    topics = row.get("topic_word", "")
    topic_list = topics.split(",") if topics else []

    for topic in topic_list:
        if topic.strip():
            driver.execute_script("arguments[0].scrollIntoView();", editor_input)
            editor_input.send_keys(f" #{topic.strip()}")
            # 等待下拉列表显示
            try:
                WebDriverWait(driver, 5).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "#quill-mention-list"))
                )
                time.sleep(3)
                # 如果下拉列表出现，按下 ENTER 键
                editor_input.send_keys(Keys.ENTER)
                time.sleep(1)
            except:
                # 如果没有找到下拉列表，跳过按键
                print(f"没有找到下拉列表，跳过 {topic.strip()}")
                continue
            # time.sleep(3)
            # editor_input.send_keys(Keys.ENTER)
            # time.sleep(1)

    # **点击发布**
    publish_btn = driver.find_element(By.XPATH, '//div[@class="d-button-content"]//span[text()="发布"]')
    publish_btn.click()
    time.sleep(1)
    print("发布完成")

    # **更新明道状态**
    try:
        # 等待元素 "发布成功"出现，最多等待 6 秒
        WebDriverWait(driver, 6).until(
            EC.text_to_be_present_in_element((By.CSS_SELECTOR, "p.title"), "发布成功")
        )
        # **更新明道状态**
        update_mingdao_status(row)
        print("明道状态更新成功")
    except Exception as e:
        print("未检测到 '发布成功'，不更新状态和窗口")
    
    # === 自动化发布完成后，开始做账号矩阵统计 ===
    try:
        # 打开浏览器并跳转创作首页
        res = open_browser(browser_id)
        driver.get("https://creator.xiaohongshu.com/new/home")
        time.sleep(3)

        # 访问小红书页面后检测登录状态
        if not check_login_status(driver):
            print(f"🔐 访问创作首页后检测到浏览器 {browser_id} 未登录，停止矩阵统计")
            name = browser_name or f"浏览器_{browser_id}"
            account_name = xhs_account or "未知账号"  # 使用传入的账号信息
            handle_login_logout(browser_id, name, account_name)
            return

        # 获取小红书号和昵称
        xhs_number, nickname = account_matrix(driver)

        if xhs_number and nickname:
            driver.get("https://creator.xiaohongshu.com/new/note-manager")
            time.sleep(3)

            # 访问笔记管理页面后再次检测登录状态
            if not check_login_status(driver):
                print(f"🔐 访问笔记管理页面后检测到浏览器 {browser_id} 未登录")
                name = browser_name or f"浏览器_{browser_id}"
                handle_login_logout(browser_id, name, xhs_number)
                return

            scrape_notes(driver, xhs_number, nickname)
        else:
            print("❌ 无法获取小红书账号信息，跳过笔记抓取")

    except Exception as e:
        print(f"❌ 出现异常：{e}")
        closeBrowser(browser_id)



def update_mingdao_status(item, status="已发布", timeout=6):
    """
    修改明道表字段状态为指定值（默认 "已发布"），针对传入的单个循环项，不做重试和异常处理。
    :param item: 包含需要更新的行记录数据（单个 JSON 对象）。
    :param status: 要设置的状态值，默认值为 "已发布"。
    :param timeout: 请求超时时间（秒），默认值为 6。
    :return: API 调用结果
    """
    url = "https://api.mingdao.com/v2/open/worksheet/editRows"
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json",
    }
    
    # 提取 rowid
    row_id = item.get("rowid")
    if not row_id:
        raise ValueError("未找到有效的 rowid 数据")  # 如果 rowid 不存在，直接抛出标准异常

    # 构建请求数据
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "jzzhnrff",
        "rowIds": [row_id],  # 使用 rowid
        "controls": [
            {
                "controlId": "release_status",
                "value": status,
                "valueType": 1
            }
        ]
    }

    # 发起 POST 请求，设置超时
    try:
        response = requests.post(url, headers=headers, json=data, timeout=timeout)
        response.raise_for_status()  # 如果响应错误，抛出 HTTP 异常
    except requests.exceptions.RequestException as e:
        raise e  # 直接抛出请求异常，外部调用会处理

    # 检查响应状态码
    res = response.json()
    if res.get("success"):
        print(f"行记录 {row_id} 的状态已更新为: {status}")
        return res
    else:
        # 如果 API 返回失败，直接抛出异常
        error_message = res.get("error", "未知错误")
        raise ValueError(f"更新失败: {error_message}")



# 查询养号配置的方法
# ✅ 返回养号配置的整条记录（dict），如果没有返回 None
def get_raise_config_row(account_number: str):
    url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    headers = {
        "Content-Type": "application/json",
    }
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "raiseAccount",
        "filters": [
            {
                "controlId": "opreateAccount",
                "dataType": 2,
                "spliceType": 1,
                "filterType": 1,
                "value": str(account_number)
            },
            {
                "controlId": "qudao",
                "dataType": 2,
                "value": 'bite'
            }

        ]
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            res = response.json()
            rows = res.get("data", {}).get("rows", [])
            if rows:
                return rows[0]  # 返回第一条记录
            else:
                return None
        else:
            print(f"请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return None
    except Exception as e:
        print(f"请求失败，异常信息: {e}")
        return None


def is_within_time_range(start_str, end_str):
    try:
        now = datetime.now().time()
        start = datetime.strptime(start_str, "%H:%M").time()
        end = datetime.strptime(end_str, "%H:%M").time()

        if start <= now <= end:
            return True
        else:
            return False
    except Exception as e:
        print(f"时间判断失败: {e}")
        return False


def like_post(driver):
    try:
        # 确保点赞按钮可点击，并滚动到目标元素
        like_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//span[contains(@class, 'like-wrapper')]"))
        )
        
        # 点击点赞按钮
        like_button.click()  # 点击点赞按钮
        print("👍 点赞成功")
    except Exception as e:
        print(f"❌ 点赞失败: {e}")


def collect_post(driver):
    try:
        collect_button = driver.find_element(By.XPATH, "//span[contains(@class, 'collect-wrapper')]")
        collect_button.click()
        print("📌 收藏成功")
    except:
        print("❌ 收藏失败")


def get_random_comment(raise_config):
    comments_str = raise_config.get("commentContents", "")
    comments = [c.strip() for c in comments_str.strip().split("\n") if c.strip()]
    return random.choice(comments) if comments else "太棒啦～"

def follow_user(driver):
    try:
        # 等待按钮可点击
        follow_button = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//button[contains(@class, 'follow-button')]"))
        )

        # 使用 JavaScript 强制点击按钮，绕过一些可见性或遮挡问题
        driver.execute_script("arguments[0].click();", follow_button)
        
        print("✅ 点击了关注按钮")

    except Exception as e:
        print(f"❌ 关注失败: {e}")


def comment_post(driver, raise_config):
    try:
        # 先点击“说点什么...”区域
        comment_trigger = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'inner')]//span[contains(text(), '说点什么')]"))
        )
        driver.execute_script("arguments[0].click();", comment_trigger)
        time.sleep(1)

        # 等待 contenteditable 元素出现
        input_box = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "//p[@contenteditable='true']"))
        )

        # 获取评论内容
        comments = raise_config.get("commentContents", "").strip().split("\n")
        comments = [c.strip() for c in comments if c.strip()]
        if not comments:
            print("⚠️ 配置中无有效评论内容")
            return

        comment_text = random.choice(comments)
        input_box.send_keys(comment_text)  # 在 contenteditable 元素中输入评论
        time.sleep(1)

        # 找到发送按钮
        send_button = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(@class, 'submit')]"))
        )

        # 使用 ActionChains 确保按钮点击
        actions = ActionChains(driver)
        actions.move_to_element(send_button).click().perform()

        print(f"💬 评论成功：{comment_text}")

    except Exception as e:
        print("❌ 评论失败")



# def view_comments(driver):
#     try:
#         comment_area = driver.find_element(By.XPATH, "//div[contains(@class, 'comment') or contains(text(), '评论')]")
#         driver.execute_script("arguments[0].scrollIntoView(true);", comment_area)
#         print("👀 浏览评论区")
#     except:
#         print("❌ 评论区加载失败")

# def visit_profile(driver):
#     try:
#         profile_link = driver.find_element(By.XPATH, "//a[contains(@href, '/user/profile/')]")
#         driver.execute_script("arguments[0].scrollIntoView(true);", profile_link)
#         profile_link.click()
#         print("👤 访问用户主页")
#         time.sleep(random.uniform(4, 8))
#         driver.back()
#     except:
#         print("❌ 访问用户主页失败")



def run_raise_account_automation(driver, raise_config, browser_id, xhs_account=None, browser_name=None):
    print(f"🧬 开始执行养号流程：浏览器 ID {browser_id}")

    driver.get("https://www.xiaohongshu.com/explore")
    print("✅ 进入小红书首页")
    time.sleep(random.uniform(5, 10))

    # 访问小红书页面后检测登录状态
    if not check_login_status(driver):
        print(f"🔐 访问首页后检测到浏览器 {browser_id} 未登录，停止养号操作")
        name = browser_name or f"浏览器_{browser_id}"
        account_name = xhs_account or "未知账号"
        handle_login_logout(browser_id, name, account_name)
        return
   
    behavior_map = {
        "like": like_post,
        "collect": collect_post,
        "comment": lambda d: comment_post(d, raise_config),
        "follow": lambda d: follow_user(d),  # 新增关注行为
        # "view_comments": view_comments,
        # "visit_profile": visit_profile,
    }

    while is_within_time_range(raise_config.get("firstStartTime"), raise_config.get("firstEndTime")):
        try:
            # 获取所有笔记元素
            posts = driver.find_elements(By.XPATH, "//section[contains(@class, 'note-item')]")
            if not posts:
                print("⚠️ 没有找到笔记，稍后重试")
                time.sleep(random.uniform(10, 20))
                continue

            post = random.choice(posts)
            try:
                link = post.find_element(By.XPATH, ".//a[contains(@class, 'cover') and contains(@href, '/explore/')]")
                
                # 在进入笔记前加入点赞的随机概率
                if random.random() < 0.5:  # 50%的概率执行点赞
                    like_button = post.find_element(By.XPATH, ".//span[contains(@class, 'like-wrapper') and contains(@class, 'like-active')]")
                    like_button.click()
                    print("✅ 在进入笔记前点击了一个点赞")
                    time.sleep(random.uniform(2, 4))

                    # 点赞后检测是否出现二维码验证
                    if check_qrcode_verification(driver):
                        print(f"📱 点赞后检测到二维码验证弹窗，停止养号操作")
                        name = browser_name or f"浏览器_{browser_id}"
                        account_name = xhs_account or "未知账号"
                        handle_qrcode_verification(browser_id, name, account_name)
                        return

                driver.execute_script("arguments[0].scrollIntoView(true);", link)
                link.click()
                print("📝 成功进入随机笔记页面")
                time.sleep(random.uniform(5, 10))

                # 检测是否出现二维码验证
                if check_qrcode_verification(driver):
                    print(f"📱 检测到二维码验证弹窗，停止养号操作")
                    name = browser_name or f"浏览器_{browser_id}"
                    account_name = xhs_account or "未知账号"
                    handle_qrcode_verification(browser_id, name, account_name)
                    return

                # 执行随机行为
                random_behaviors = {
                    # "like": 0.6,
                    "collect": 0.3,
                    "comment": 0.2,
                    "follow": 0.1,  # 添加关注的概率
                    # "view_comments": 0.5,
                    # "visit_profile": 0.3
                }

                for behavior, prob in random_behaviors.items():
                    if random.random() < prob:
                        func = behavior_map.get(behavior)
                        if func:
                            print(f"🎯 执行行为: {behavior}")
                            func(driver)

                            # 执行行为后检测是否出现二维码验证
                            time.sleep(2)  # 等待可能的验证弹窗
                            if check_qrcode_verification(driver):
                                print(f"📱 执行 {behavior} 行为后检测到二维码验证弹窗，停止养号操作")
                                name = browser_name or f"浏览器_{browser_id}"
                                account_name = xhs_account or "未知账号"
                                handle_qrcode_verification(browser_id, name, account_name)
                                return

                        time.sleep(random.uniform(2, 4))

                # 停留一段时间模拟阅读
                stay_time = random.uniform(5, 15)
                print(f"⏱ 停留 {stay_time:.1f} 秒模拟阅读")
                time.sleep(stay_time)

                driver.back()
                time.sleep(random.uniform(3, 6))

            except Exception as e:
                print(f"⚠️ 进入笔记异常")

        except Exception as e:
            print(f"❌ 养号流程异常: {e}")

        # 每轮间隔几秒
        # interval = random.uniform(10, 20)
        # print(f"🔄 准备进入下一轮，等待 {interval:.1f} 秒")
        # time.sleep(interval)

    print("⏹ 当前时间不在配置范围内，停止养号流程")


def run_yanghao_automation(browser_id, browser, vps_mapping, raise_config, ip_name, xhs_account=None):
    # === 1. 获取 VPS 信息，判断是否需要更新 IP ===

    static_ips = {"眼视光-临沂1", "眼视光-临沂2"}
    if ip_name in static_ips:
        print(f"📌 {ip_name} 是静态 IP，跳过 IP 检测逻辑")
    else:
        vps_info = vps_mapping.get(ip_name)

        if not vps_info:
            print(f"⚠️ 未找到 {ip_name} 的 VPS 配置，跳过 IP 检测")
        else:
            current_ip = browser.get("lastIp", "")
            expected_ip = get_ppp0_ip(vps_info)  # 通过 SSH 获取 ppp0 的 IP

            if expected_ip and expected_ip != current_ip:
                print(f"⚠️ 浏览器 ID {browser_id} IP 变更: {current_ip} → {expected_ip}")

                update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
                time.sleep(2)

                closeBrowser(browser_id)
                time.sleep(15)
                print(f"✅ 浏览器 ID {browser_id} 已关闭，应用新 IP {expected_ip}")

                # 更新本地 browser_map 中的 IP 信息
                browser["lastIp"] = expected_ip
            else:
                print(f"当前 IP 未变更")

    try:
        res = open_browser(browser_id)
        driver_path = res['data']['driver']
        debugger_address = res['data']['http']
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("debuggerAddress", debugger_address)
        chrome_service = Service(driver_path)
        driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

        browser_name = browser.get("name", f"浏览器_{browser_id}")
        run_raise_account_automation(driver, raise_config, browser_id, xhs_account or ip_name, browser_name)
        closeBrowser(browser_id)
        driver.quit()
    except Exception as e:
        print(f"关闭浏览器 {browser_id}，错误信息：{e}")
        closeBrowser(browser_id)


# 创建浏览器并打开
def open_browser_with_id_and_run_automation(browser_map, browser_id, vps_mapping):
    browser = browser_map[browser_id]

    try:
        # 从备注中提取小红书号（格式如：8762500520_nara-重庆）
        xhs_remark = browser.get("remark")
        if "_" not in xhs_remark:
            print(f"备注格式错误，无法提取小红书号：{xhs_remark}")
            return

        xhs_account = xhs_remark.split("_")[0]
        raise_ip_name = xhs_remark.split("_")[1]
        print(f"提取到小红书账号: {xhs_account}")

        # ✅ 优先检查是否有发布任务
        row = check_fabu_config(browser)

        if row:
            print(f"📣 账号 {xhs_account} 检测到发布任务，跳过养号流程，开始发布流程")

            # === 1. 获取 VPS 信息，判断是否需要更新 IP ===
            ip_name = row.get("ip_name")
            static_ips = {"眼视光-临沂1", "眼视光-临沂2"}
            if ip_name in static_ips:
                print(f"📌 {ip_name} 是静态 IP，跳过 IP 检测逻辑")
            else:
                vps_info = vps_mapping.get(ip_name)
                if not vps_info:
                    print(f"⚠️ 未找到 {ip_name} 的 VPS 配置，跳过 IP 检测")
                else:
                    current_ip = browser.get("lastIp", "")
                    expected_ip = get_ppp0_ip(vps_info)
                    if expected_ip and expected_ip != current_ip:
                        print(f"⚠️ 浏览器 ID {browser_id} IP 变更: {current_ip} → {expected_ip}")
                        update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
                        time.sleep(2)
                        closeBrowser(browser_id)
                        time.sleep(30)
                        print(f"✅ 浏览器 ID {browser_id} 已关闭，应用新 IP {expected_ip}")
                        browser["lastIp"] = expected_ip
                    else:
                        print(f"当前 IP 未变更")

            # === 2. 打开浏览器并运行自动化 ===
            res = open_browser(browser_id)
            driver_path = res['data']['driver']
            debugger_address = res['data']['http']
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_experimental_option("debuggerAddress", debugger_address)
            chrome_service = Service(driver_path)
            driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

            browser_name = browser.get("name", f"浏览器_{browser_id}")
            run_automation(driver, row, browser_id, xhs_account, browser_name)
            closeBrowser(browser_id)
            return  # 发布完成，直接 return

        # ✅ 如果没有发布任务，再尝试养号逻辑
        raise_config = get_raise_config_row(xhs_account)
        if raise_config:
            print(f"账号 {xhs_account} 存在养号配置")
            if is_within_time_range(raise_config.get("firstStartTime"), raise_config.get("firstEndTime")):
                run_yanghao_automation(browser_id, browser, vps_mapping, raise_config, raise_ip_name, xhs_account)
            else:
                print("⏹ 当前时间不在配置范围内，停止养号流程")
        else:
            print(f"账号 {xhs_account} 不在养号配置中")

    except Exception as e:
        print(f"❌ 执行过程中发生异常: {e}")
        closeBrowser(browser_id)


def get_existing_browser_map():
    """获取当前所有浏览器的完整信息映射"""
    browser_map = {}
    browser_data = get_browser_list(page=0, page_size=100)

    if not browser_data or not browser_data['success']:
        print("获取浏览器列表失败，程序退出。")
        time.sleep(10)
        return browser_map

    for browser in browser_data['data']['list']:
        browser_id = browser['id']
        browser_map[browser_id] = browser  # 直接存整个 browser 对象

    return browser_map


def extract_ip_from_account(account):
    """从账号配置中提取 IP 地址"""
    try:
        extra_data_list = json.loads(account.get('67d2bf436c09cd6ec047059b', '[]'))
        if isinstance(extra_data_list, list) and extra_data_list:
            first_item = extra_data_list[0]
            source_value = first_item.get("sourcevalue", "{}")
            source_data = json.loads(source_value)
            return source_data.get("678c76dc2e29d4dda9f251b6", None)  # 最新 IP
    except json.JSONDecodeError:
        print(f"账号 {account.get('zhmc', '未知')} 的数据解析失败")
    return None

def create_missing_browsers(account_configs, vps_mapping):
    """创建未创建的浏览器，返回 browser_id -> ip 映射"""
    browser_ip_map = {}

    for account in account_configs:
        bite_window_status = account.get('bite_window_status', '')

        ip_address = extract_ip_from_account(account)

        if bite_window_status == '未创建' and ip_address:
            
            name = account.get("ip_name")
            vps_info = vps_mapping.get(name)
            if not vps_info:
                print(f"⚠️ 未找到 {name} 的 VPS 配置，跳过")
                continue

            browser_id = createBrowser(ip_address, account, vps_info)
            if browser_id:
                print(f"✅ 已创建浏览器 ID {browser_id}，IP: {ip_address}")
                update_browser_status(account, browser_id, "创建成功")
                browser_ip_map[browser_id] = ip_address  # 记录新创建的浏览器 IP
            else:
                print(f"❌ 创建浏览器失败，IP: {ip_address}")

    return browser_ip_map


def fetch_logged_in_browser_ids(account_configs, browser_map):
    """获取已登录的浏览器id"""
    browser_ids = []

    for account in account_configs:
        browser_id = account.get("bite_window_id")
        if not browser_id or browser_id not in browser_map:
            continue

        # 只有当 bite_window_status 为 "已登录" 时，才添加到 browser_ids
        if account.get("bite_window_status") == "已登录":
            browser_ids.append(browser_id)

    return browser_ids


def detect_and_update_browser_ips(account_configs, browser_map, vps_mapping):
    """检测 IP 变更并更新代理"""
    updated_browser_map = browser_map.copy()
    browser_ids = []

    for account in account_configs:
        browser_id = account.get("bite_window_id")
        if not browser_id or browser_id not in browser_map:
            continue

        name = account.get("ip_name")
        vps_info = vps_mapping.get(name)
        if not vps_info:
            print(f"⚠️ 未找到 {name} 的 VPS 配置，跳过")
            continue

        browser = browser_map[browser_id]  # 获取完整的浏览器对象
        current_ip = browser.get("lastIp", "")  # 从浏览器对象获取当前 IP
        # expected_ip = extract_ip_from_account(account)  # 获取最新的 IP

        expected_ip = get_ppp0_ip(vps_info)

        if expected_ip and expected_ip != current_ip:
            print(f"⚠️ 浏览器 ID {browser_id} IP 变更: {current_ip} → {expected_ip}")

            update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
            time.sleep(2)

            closeBrowser(browser_id)
            time.sleep(30)
            print(f"✅ 浏览器 ID {browser_id} 已关闭，应用新 IP {expected_ip}")
            # openBrowser(browser_id)
            # print(f"✅ 浏览器 ID {browser_id} 已重启，应用新 IP {expected_ip}")

            # 更新浏览器对象中的 IP（这里假设浏览器对象是个可变字典）
            updated_browser_map[browser_id]["lastIp"] = expected_ip

        # 只有当 bite_window_status 为 "已登录" 时，才添加到 browser_ids
        if account.get("bite_window_status") == "已登录":
            browser_ids.append(browser_id)

    return browser_ids



def run_automation_concurrently(browser_ids, browser_map, vps_mapping, max_workers=3, delay=8):
    """最多 max_workers 并发，任务启动间隔 delay 秒"""
    if not browser_ids:
        print("⚠️ 没有需要执行的浏览器，等待 30 秒后重试")
        time.sleep(30)
        return

    print(f"🚀 启动自动化任务，总数 {len(browser_ids)}，最大并发 {max_workers}，每个延迟 {delay}s")

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for index, browser_id in enumerate(browser_ids):
            # 将 delay 作为参数传进线程里，错峰启动
            future = executor.submit(open_browser_with_id_and_run_automation, browser_map, browser_id, vps_mapping)
            futures.append(future)
            time.sleep(5) 

        # 等待所有任务完成
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"❌ 自动化任务出错: {e}")

    print("✅ 所有自动化任务完成")



# def run_automation_concurrently(browser_ids, browser_map):
#     """使用并发执行自动化，每个线程间隔 8 秒启动"""
#     if not browser_ids:
#         print("未找到已登录的浏览器 ID，程序等待 60 秒后重试。")
#         time.sleep(60)
#         return

#     batch_size = 3  # 每次 3 个线程

#     for i in range(0, len(browser_ids), batch_size):
#         batch = browser_ids[i:i + batch_size]

#         with concurrent.futures.ThreadPoolExecutor(max_workers=len(batch)) as executor:
#             future_to_browser_id = {
#                 executor.submit(open_browser_with_id_and_run_automation, browser_map, browser_id, index * 8): browser_id
#                 for index, browser_id in enumerate(batch)
#             }

#             for future in concurrent.futures.as_completed(future_to_browser_id):
#                 browser_id = future_to_browser_id[future]
#                 try:
#                     future.result()
#                     print(f"✅ 浏览器 ID {browser_id} 自动化操作执行完毕")
#                 except Exception as e:
#                     print(f"❌ 浏览器 ID {browser_id} 执行自动化操作时出错: {e}")

#         # 等待所有线程执行完毕后再进入下一批次
#         time.sleep(8 * batch_size)

MINGDAO_API = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
MINGDAO_HEADERS = {
    "Content-Type": "application/json"
}
MINGDAO_PAYLOAD = {
    "appKey": "f08bf7f7cfe8c038",
    "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
    "worksheetId": "proxy_ip",
    "controls": [],
    "filters": []
}

def fetch_vps_mapping():
    resp = requests.post(MINGDAO_API, json=MINGDAO_PAYLOAD, headers=MINGDAO_HEADERS)
    data = resp.json()["data"]["rows"]
    mapping = {}
    for row in data:
        name = row.get("name")
        mapping[name] = {
            "server_ip": row.get("server_ip"),
            "server_port": int(row.get("server_port")),
            "password": row.get("password"),
            "username": "root",
            "wsport": row.get("wsport"),
        }
    return mapping

def get_ppp0_ip(info):
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(
            hostname=info["server_ip"],
            port=info["server_port"],
            username=info["username"],
            password=info["password"]
        )
        command = "ip addr show ppp0 | grep inet | awk '{print $2}' | cut -d/ -f1"
        _, stdout, stderr = ssh.exec_command(command)

        result = stdout.read().decode().strip()
        err = stderr.read().decode().strip()

        ssh.close()

        if result:
            print(f"✅ 成功获取 ppp0 IP：{result}")
            return result
        else:
            print("⚠️ 没有获取到 IP，可能 ppp0 接口未启用")
            if err:
                print("错误信息：", err)
            return None

    except Exception as e:
        print(f"❌ SSH 连接失败：{e}")
        return None

def keepalive_with_ip_check(browser_id, browser_map, vps_mapping, keepalive_time_map):
    print(f"🌀 浏览器 {browser_id} 进入保活流程")

    browser = browser_map.get(browser_id)
    remark = browser.get("remark", "")
    parts = remark.split("_")
    ip_name = parts[1] if len(parts) > 1 else None

    if ip_name and ip_name not in {"眼视光-临沂1", "眼视光-临沂2"}:
        vps_info = vps_mapping.get(ip_name)
        if vps_info:
            current_ip = browser.get("lastIp", "")
            expected_ip = get_ppp0_ip(vps_info)
            if expected_ip and expected_ip != current_ip:
                print(f"⚠️ 浏览器 {browser_id} IP 变更: {current_ip} → {expected_ip}")
                update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
                time.sleep(2)
                browser["lastIp"] = expected_ip
            else:
                print(f"✅ 浏览器 {browser_id} IP 未变更")
        else:
            print(f"⚠️ 未找到 {ip_name} 的 VPS 信息，跳过 IP 检测")
    else:
        print(f"📌 {ip_name} 是静态 IP 或缺失，跳过检测")

    try:
        res = open_browser(browser_id)
        driver_path = res['data']['driver']
        debugger_address = res['data']['http']

        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("debuggerAddress", debugger_address)
        chrome_service = Service(driver_path)
        driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

        driver.get('https://creator.xiaohongshu.com/publish/publish?source=official')
        time.sleep(8)

        # 访问小红书页面后检测登录状态
        if not check_login_status(driver):
            print(f"🔐 访问发布页面后检测到浏览器 {browser_id} 未登录，停止保活操作")
            browser_name = browser.get("name", f"浏览器_{browser_id}")
            xhs_account = ip_name or "未知账号"
            handle_login_logout(browser_id, browser_name, xhs_account)
            driver.quit()
            return

        closeBrowser(browser_id)
        keepalive_time_map[browser_id] = time.time()
        print(f"✅ 浏览器 {browser_id} 保活完成并已关闭")
    except Exception as e:
        print(f"❌ 浏览器 {browser_id} 保活失败: {e}")



def main(bite_user_name, platform):
    bite_user_name = input("请输入启动用户名: ")

    keepalive_time_map = {}
    first_loop = True  # ✅ 标记首次循环

    while True:
        try:
            # VPS 信息映射
            vps_mapping = fetch_vps_mapping()
            # print(vps_mapping)

            account_configs = fetch_account_config(bite_user_name)

            # 1. 创建未创建的浏览器
            create_missing_browsers(account_configs, vps_mapping)

            # 2. 获取所有浏览器 ID -> 浏览器对象的完整映射
            browser_map = get_existing_browser_map()

            # 3. 获取登录的浏览器
            # browser_ids = detect_and_update_browser_ips(account_configs, browser_map ,vps_mapping)
            browser_ids = fetch_logged_in_browser_ids(account_configs, browser_map)

            # 5. 执行自动化操作
            run_automation_concurrently(browser_ids, browser_map, vps_mapping)

            print("✅ 所有自动化任务执行完毕，等待 5 秒后重新获取浏览器列表。")
        except Exception as e:
            print(f"⚠️ 发生异常: {e}")

        first_loop = False  # 标记为已执行过
        time.sleep(5)


def start_gui():
    """弹出 GUI 让用户输入用户名 & 选择平台"""

    def on_submit():
        username = entry.get().strip()
        platform = dropdown_var.get().strip()
        if username and platform:
            root.destroy()  # 关闭窗口
            main(username, platform)  # 运行主程序

    root = tk.Tk()
    root.title("自动化启动配置")

    # 让窗口居中
    window_width, window_height = 350, 220
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    position_x = (screen_width - window_width) // 2
    position_y = (screen_height - window_height) // 2
    root.geometry(f"{window_width}x{window_height}+{position_x}+{position_y}")

    # 用户名输入框
    tk.Label(root, text="请输入启动用户名:", font=("Arial", 12)).pack(pady=8)
    entry = tk.Entry(root, font=("Arial", 12), width=25)
    entry.pack(pady=5)

    # 选择平台
    tk.Label(root, text="选择一个平台:", font=("Arial", 12)).pack(pady=8)
    dropdown_var = tk.StringVar(value="小红书")  # 默认值
    dropdown = ttk.Combobox(root, textvariable=dropdown_var, values=["小红书", "抖音"], font=("Arial", 12), width=22)
    dropdown.pack(pady=5)

    # 提交按钮（加大尺寸）
    submit_button = tk.Button(root, text="提交", font=("Arial", 12, "bold"), width=15, height=2, command=on_submit)
    submit_button.pack(pady=15)

    root.mainloop()


if __name__ == "__main__":
    main(None,None)
    # start_gui()


