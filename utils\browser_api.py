import requests
import json
from config.config import BIT_BROWSER_CONFIG
from utils.logger import setup_logger

logger = setup_logger("browser_api")

def update_browser_status(item, browser_id, status="创建成功", timeout=6):
    """更新浏览器状态"""
    try:
        url = f"{BIT_BROWSER_CONFIG['api_address']}/browser/update"
        data = {
            "id": browser_id,
            "name": item.get("name", ""),
            "remark": item.get("remark", ""),
            "status": status
        }
        
        response = requests.post(url, json=data, timeout=timeout)
        return response.json()
        
    except Exception as e:
        logger.error(f"更新浏览器状态失败: {e}")
        return None

def create_browser_no_proxy(account):
    """创建无代理浏览器"""
    try:
        url = f"{BIT_BROWSER_CONFIG['api_address']}/browser/create"
        data = {
            "name": f"XHS_{account}",
            "remark": f"{account}_无代理",
            "proxy": {
                "mode": "noproxy"
            }
        }
        
        response = requests.post(url, json=data, timeout=BIT_BROWSER_CONFIG['timeout'])
        return response.json()
        
    except Exception as e:
        logger.error(f"创建无代理浏览器失败: {e}")
        return None

def create_browser_with_proxy(host, account, vps_info):
    """创建代理浏览器"""
    try:
        url = f"{BIT_BROWSER_CONFIG['api_address']}/browser/create"
        data = {
            "name": f"XHS_{account}",
            "remark": f"{account}_代理",
            "proxy": {
                "mode": "http",
                "host": host,
                "port": vps_info.get("port", 0),
                "username": vps_info.get("username", ""),
                "password": vps_info.get("password", "")
            }
        }
        
        response = requests.post(url, json=data, timeout=BIT_BROWSER_CONFIG['timeout'])
        return response.json()
        
    except Exception as e:
        logger.error(f"创建代理浏览器失败: {e}")
        return None

def get_browser_detail(browser_id):
    """获取浏览器详情"""
    try:
        url = f"{BIT_BROWSER_CONFIG['api_address']}/browser/detail"
        params = {"id": browser_id}
        
        response = requests.get(url, params=params, timeout=BIT_BROWSER_CONFIG['timeout'])
        return response.json()
        
    except Exception as e:
        logger.error(f"获取浏览器详情失败: {e}")
        return None

def open_browser(browser_id):
    """打开浏览器"""
    try:
        url = f"{BIT_BROWSER_CONFIG['api_address']}/browser/open"
        data = {"id": browser_id}
        
        response = requests.post(url, json=data, timeout=BIT_BROWSER_CONFIG['timeout'])
        return response.json()
        
    except Exception as e:
        logger.error(f"打开浏览器失败: {e}")
        return None

def close_browser(browser_id):
    """关闭浏览器"""
    try:
        url = f"{BIT_BROWSER_CONFIG['api_address']}/browser/close"
        data = {"id": browser_id}
        
        response = requests.post(url, json=data, timeout=BIT_BROWSER_CONFIG['timeout'])
        return response.json()
        
    except Exception as e:
        logger.error(f"关闭浏览器失败: {e}")
        return None

def delete_browser(browser_id):
    """删除浏览器"""
    try:
        url = f"{BIT_BROWSER_CONFIG['api_address']}/browser/delete"
        data = {"id": browser_id}
        
        response = requests.post(url, json=data, timeout=BIT_BROWSER_CONFIG['timeout'])
        return response.json()
        
    except Exception as e:
        logger.error(f"删除浏览器失败: {e}")
        return None

def get_browser_list(page=0, page_size=100):
    """获取浏览器列表"""
    try:
        url = f"{BIT_BROWSER_CONFIG['api_address']}/browser/list"
        params = {
            "page": page,
            "pageSize": page_size
        }
        
        response = requests.get(url, params=params, timeout=BIT_BROWSER_CONFIG['timeout'])
        return response.json()
        
    except Exception as e:
        logger.error(f"获取浏览器列表失败: {e}")
        return None 