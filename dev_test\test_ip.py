import paramiko

# VPS 登录信息
vps_info = {
    "server_ip": "jiaxing01.ipdog.cn",
    "server_port": 12078,
    "password": "http098390Ls",
    "username": "root"
}

def get_ppp0_ip(info):
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(
            hostname=info["server_ip"],
            port=info["server_port"],
            username=info["username"],
            password=info["password"]
        )

        command = "ip addr show ppp0 | grep inet | awk '{print $2}' | cut -d/ -f1"
        _, stdout, stderr = ssh.exec_command(command)

        result = stdout.read().decode().strip()
        err = stderr.read().decode().strip()

        ssh.close()

        if result:
            print(f"✅ 成功获取到 ppp0 IP：{result}")
        else:
            print("⚠️ 没有获取到 IP，可能 ppp0 接口未启用")
            if err:
                print("错误信息：", err)

    except Exception as e:
        print(f"❌ SSH 连接失败或执行命令失败：{e}")

# 运行函数
get_ppp0_ip(vps_info)
