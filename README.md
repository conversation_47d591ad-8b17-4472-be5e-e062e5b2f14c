# 小红书自动化工具

这是一个用于小红书平台自动化的工具集，支持内容分发、账号养号、监控和内容抓取等功能。

## 功能特点

- **分发和养号**：自动发布内容并进行账号养号操作
- **监控和分发**：监控通知并自动回复，同时支持内容分发
- **内容抓取**：支持抓取笔记、评论等数据

## 项目结构

```
browser-auto/
├── config/             # 配置文件目录
│   └── config.py      # 主配置文件
├── sources/           # 核心功能实现
│   ├── xhs_dispense.py    # 分发和养号功能
│   ├── xhs_spider.py      # 内容抓取功能
│   └── abmon_dispense.py  # 监控和分发功能
├── utils/             # 工具类
│   ├── logger.py      # 日志工具
│   └── browser_api.py # 浏览器 API 工具
├── logs/              # 日志文件目录
├── main.py           # 主程序入口
└── README.md         # 项目说明文档
```

## 环境要求

- Python 3.8+
- Chrome 浏览器
- BitBrowser 浏览器

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行主程序：
```bash
python main.py
```

2. 在菜单中选择要执行的功能：
   - 1: 分发和养号
   - 2: 监控和分发
   - 3: 抓取
   - 4: 退出

## 配置说明

在 `config/config.py` 中配置以下内容：

- BitBrowser API 配置
- 明道云 API 配置
- 日志配置
- 其他功能配置

## 注意事项

1. 使用前请确保已正确配置 BitBrowser 和明道云的相关参数
2. 建议在运行前先测试单个功能
3. 请遵守平台规则，合理使用自动化功能

## 日志说明

- 日志文件保存在 `logs` 目录下
- 日志级别可在配置文件中调整
- 支持同时输出到控制台和文件

## 错误处理

- 程序会自动处理常见的异常情况
- 详细的错误信息会记录在日志文件中
- 支持优雅退出

## 打包命令
pip install pyinstaller
pyinstaller --onefile --name biteAuto --clean main.py 