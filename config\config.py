import logging
from configobj import ConfigObj
from utils.logger import setup_logger
import os

logger = setup_logger("config")

# 从配置文件加载配置
try:
    config = ConfigObj("config.ini")
    DEBUG = config.as_bool("debug") if "debug" in config else False # 支持类型转换, 提供默认值
    BITBROWSER_URL = config.get("bitbrowser_url", "http://127.0.0.1:54345")  # 提供默认值

    logger.info("Configuration loaded successfully from config.ini")
    logger.debug(f"Configuration: DEBUG={DEBUG}, BITBROWSER_URL={BITBROWSER_URL}")

except Exception as e:
    logger.error(f"Error loading configuration: {e}")
    raise

class BitbrowserSettings:
    def __init__(self, url):
        self.url = url
    def __str__(self):
        return f"url={self.url}"


bitbrowser_settings = BitbrowserSettings(BITBROWSER_URL)
# 打印加载完毕的变量，方便查看
print(f"bitbrowser_settings: {bitbrowser_settings}")

# API配置
APP_KEY = "f08bf7f7cfe8c038"
SIGN = "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ=="

# 工作表ID
JZTJ_WORKSHEET_ID = "your_jztj_worksheet_id"
BJK_WORKSHEET_ID = "your_bjk_worksheet_id"
RAISE_WORKSHEET_ID = "your_raise_worksheet_id"

# 浏览器配置
MAX_WORKERS = 5
BROWSER_TIMEOUT = 30
PAGE_LOAD_TIMEOUT = 30

# 任务配置
TASK_DELAY = 5
MAX_RETRY = 3

# 文件配置
MEDIA_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), "media")

# 监控配置
MAX_USER_COUNT = 100
MONITOR_INTERVAL = 60

# 养号配置
RAISE_CONFIG = {
    "like_probability": 0.7,
    "collect_probability": 0.5,
    "comment_probability": 0.3,
    "follow_probability": 0.4
}

# API端点
API_ENDPOINTS = {
    "mingdao": "https://api.mingdao.com",
    "bit_browser": "http://localhost:54345"
}

# Bit浏览器配置
BIT_BROWSER_CONFIG = {
    "api_address": "http://localhost:54345",
    "timeout": 30
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs", "app.log")
}