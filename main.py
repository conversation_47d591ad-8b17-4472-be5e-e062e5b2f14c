import sys
from utils.logger import setup_logger
from sources import xhs_publish_only

logger = setup_logger("main")

def main():
    """主程序入口"""
    logger.info("程序启动")

    try:
        # 直接调用 main 函数，现在支持交互式选择模式
        xhs_publish_only.main(None, None)
    except Exception as e:
        logger.error(f"执行时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.exception(f"程序执行出错: {e}")
        sys.exit(1)
