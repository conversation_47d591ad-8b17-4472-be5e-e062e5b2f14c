#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书统计功能测试脚本
"""

import sys
import os

# 添加sources目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'sources'))

from xhs_publish_only import (
    run_statistics_for_browser,
    run_statistics_for_all_browsers,
    get_existing_browser_map,
    test_statistics_only
)

def test_single_browser():
    """测试单个浏览器的统计功能"""
    print("🧪 测试单个浏览器统计功能")
    
    # 获取可用的浏览器
    browser_map = get_existing_browser_map()
    if not browser_map:
        print("❌ 未找到任何浏览器")
        return
    
    # 选择第一个浏览器进行测试
    browser_id = list(browser_map.keys())[0]
    print(f"🎯 选择浏览器 {browser_id} 进行测试")
    
    # 运行统计
    success = run_statistics_for_browser(browser_id)
    if success:
        print("✅ 单个浏览器统计测试成功")
    else:
        print("❌ 单个浏览器统计测试失败")

def test_all_browsers():
    """测试所有浏览器的统计功能"""
    print("🧪 测试所有浏览器统计功能")
    
    run_statistics_for_all_browsers()
    print("✅ 所有浏览器统计测试完成")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 小红书统计功能测试")
    print("=" * 60)
    
    while True:
        print("\n请选择测试选项:")
        print("1. 🚀 快速测试统计功能（推荐）")
        print("2. 测试单个浏览器统计")
        print("3. 测试所有浏览器统计")
        print("4. 查看可用浏览器")
        print("0. 退出")

        choice = input("\n请输入选项 (0-4): ").strip()

        if choice == "1":
            test_statistics_only()
        elif choice == "2":
            test_single_browser()
        elif choice == "3":
            test_all_browsers()
        elif choice == "4":
            browser_map = get_existing_browser_map()
            if browser_map:
                print(f"\n📊 找到 {len(browser_map)} 个浏览器:")
                for browser_id in browser_map.keys():
                    print(f"  - 浏览器 ID: {browser_id}")
            else:
                print("\n❌ 未找到任何浏览器")
        elif choice == "0":
            print("👋 退出测试")
            break
        else:
            print("❌ 无效选项，请重新选择")

if __name__ == "__main__":
    main()
