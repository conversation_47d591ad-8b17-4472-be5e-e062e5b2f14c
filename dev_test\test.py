# import time
# import json
# import requests
# from selenium import webdriver
# from selenium.webdriver.chrome.service import Service

# url = "http://127.0.0.1:54345"
# headers = {"Content-Type": "application/json"}

# def update_proxy_config(browser_id, proxy_type):
#     """更新代理配置并重启浏览器"""
#     json_data = {
#         'ids': [browser_id],
#         'proxyType': proxy_type,
#     }
#     try:
#         res = requests.post(f"{url}/browser/update/partial", data=json.dumps(json_data), headers=headers)
#         res.raise_for_status()
#         print(f"🛰️ 更新代理响应：{res.json()}")
#     except Exception as e:
#         print(f"❌ 更新代理失败: {e}")


# def start_chrome_with_debugger(browser_id):
#     """启动浏览器并返回 WebDriver 实例"""
#     res = openBrowser(browser_id)
#     driver_path = res['data']['driver']
#     debugger_address = res['data']['http']

#     chrome_options = webdriver.ChromeOptions()
#     chrome_options.add_experimental_option("debuggerAddress", debugger_address)
#     chrome_service = Service(driver_path)
#     driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
#     return driver


# def ensure_proxy_ip(browser_id, browser, ip_name):
#     """根据 VPS 状态判断是否需要更新代理 IP"""
#     static_ips = {"眼视光-临沂1", "眼视光-临沂2"}
#     if ip_name in static_ips:
#         print(f"📌 {ip_name} 是静态 IP，跳过 IP 检测逻辑")
#         return

#     vps_info = vps_mapping.get(ip_name)
#     if not vps_info:
#         print(f"⚠️ 未找到 {ip_name} 的 VPS 配置，跳过 IP 检测")
#         return

#     current_ip = browser.get("lastIp", "")
#     expected_ip = get_ppp0_ip(vps_info)  # 通过 SSH 获取 ppp0 的 IP

#     if expected_ip and expected_ip != current_ip:
#         print(f"⚠️ 浏览器 ID {browser_id} IP 变更: {current_ip} → {expected_ip}")
#         update_proxy([browser_id], "2", "socks5", expected_ip, "65531", "in", "zheshigesocks5")
#         time.sleep(2)
#         closeBrowser(browser_id)
#         time.sleep(15)
#         print(f"✅ 浏览器 ID {browser_id} 已关闭，应用新 IP {expected_ip}")
#         browser["lastIp"] = expected_ip
#     else:
#         print("当前 IP 未变更")


# def notification_monitor_worker(browser_id, max_user_count, browser_map):
#     try:
#         browser = browser_map[browser_id]
#         name = browser.get("name")
#         xhs_remark = browser.get("remark")
#         if "_" not in xhs_remark:
#             print(f"备注格式错误，无法提取小红书号：{xhs_remark}")
#             return

#         xhs_account, ip_name = xhs_remark.split("_", 1)

#         # 启动浏览器
#         driver = start_chrome_with_debugger(browser_id)

#         try:
#             row = check_fabu_config(browser)
#             if row:

#                 # 换ip+关闭窗口+等待15秒
#                 ensure_proxy_ip(browser_id, browser, ip_name)

#                 print(f"\n===== {name} 正在执行发布任务 =====")
#                 driver = start_chrome_with_debugger(browser_id)
#                 run_automation(driver, row, browser_id)

#                 update_proxy_config(browser_id, 'noproxy')

#                 # 重启浏览器
#                 closeBrowser(browser_id)
#                 time.sleep(15)  # 等待完全关闭
#                 driver = start_chrome_with_debugger(browser_id)

#             print(f"\n===== {name} 正在处理通知 =====")
#             process_notification(driver, browser_id, browser_map, max_user_count=max_user_count)

#         except Exception as e:
#             print(f"❌ 浏览器 {name} 任务异常：{e}")
#         finally:
#             driver.quit()
#             print(f"🛑 浏览器 {name} 任务完成，已关闭")

#     except Exception as e:
#         print(f"❌ 启动浏览器失败：{e}")






# def monitor_notifications_loop(max_user_count=10):
#     if len(sys.argv) > 1:
#         bite_user_name = sys.argv[1]
#     else:
#         bite_user_name = input("请输入启动用户名: ")

#     # ✅ 在循环外初始化线程池（设置最大并发数）
#     max_workers = 10  # 建议设置为固定数，如10，避免浏览器数量过多时创建太多线程
#     executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)

#     while True:
#         try:
#             print("🔄 检查配置和浏览器状态...")
#             account_configs = fetch_account_config(bite_user_name)

#             # 创建未创建的浏览器
#             create_missing_browsers(account_configs)

#             # 获取浏览器映射表
#             browser_map = get_existing_browser_map()

#             # 获取已登录的浏览器 ID
#             browser_ids = fetch_logged_in_browser_ids(account_configs, browser_map)

#             if not browser_ids:
#                 print("⚠️ 没有登录的浏览器，等待 30 秒后重试")
#                 time.sleep(30)
#                 continue

#             print(f"🚀 启动任务，总数 {len(browser_ids)}，最大并发 {max_workers}")

#             futures = []
#             for browser_id in browser_ids:
#                 future = executor.submit(notification_monitor_worker, browser_id, max_user_count, browser_map)
#                 futures.append(future)
#                 time.sleep(2)  # 控制线程启动间隔，防止瞬时压力过大

#             # 等待所有任务完成
#             for future in concurrent.futures.as_completed(futures):
#                 try:
#                     future.result()
#                 except Exception as e:
#                     print(f"❌ 任务出错: {e}")

#             print("✅ 一轮任务完成，随机 30~60 秒后继续...\n")

#         except Exception as e:
#             print(f"💥 主循环异常：{e}")

#         sleep_time = random.randint(30, 60)
#         time.sleep(sleep_time)