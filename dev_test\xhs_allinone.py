import sys
import time
import random
import concurrent.futures
from abmon_dispense import (
    fetch_vps_mapping as fetch_vps_mapping_abmon,
    get_existing_browser_map as get_existing_browser_map_abmon,
    fetch_logged_in_browser_ids as fetch_logged_in_browser_ids_abmon,
    create_missing_browsers as create_missing_browsers_abmon,
    notification_monitor_worker,
)
from xhs_dispense import (
    get_existing_browser_map as get_existing_browser_map_xhs,
    fetch_logged_in_browser_ids as fetch_logged_in_browser_ids_xhs,
    create_missing_browsers as create_missing_browsers_xhs,
    open_browser_with_id_and_run_automation,
)


def main():
    print("进入run")
    print("请选择运行模式：")
    print("1. 分发+养号（推荐用于日常内容分发和养号）")
    print("2. 监控+分发（推荐用于通知监控和自动回复）")
    print("3. 三合一（自动判断每个账号当前任务，优先分发，其次养号，最后监控）")
    mode = input("请输入模式编号（1/2/3）：").strip()
    if mode not in {"1", "2", "3"}:
        print("输入有误，退出。")
        return

    bite_user_name = input("请输入启动用户名: ").strip()
    if not bite_user_name:
        print("用户名不能为空，退出。")
        return

    while True:
        try:
            # 1. 获取 VPS 信息
            vps_mapping = fetch_vps_mapping_abmon()

            # 2. 获取账号配置（用 abmon/xhs 任一实现都可，假设 fetch_account_config 在 abmon_dispense.py/xhs_dispense.py 中有定义）
            from abmon_dispense import fetch_account_config
            account_configs = fetch_account_config(bite_user_name)

            # 3. 创建未创建的浏览器
            create_missing_browsers_abmon(account_configs, vps_mapping)

            # 4. 获取所有浏览器信息
            browser_map = get_existing_browser_map_abmon()

            # 5. 获取已登录的浏览器ID
            browser_ids = fetch_logged_in_browser_ids_abmon(account_configs, browser_map)

            if not browser_ids:
                print("⚠️ 没有登录的浏览器，等待 30 秒后重试")
                time.sleep(30)
                continue

            print(f"🚀 启动任务，总数 {len(browser_ids)}，最大并发 {min(len(browser_ids), 5)}")
            with concurrent.futures.ThreadPoolExecutor(max_workers=min(len(browser_ids), 5)) as executor:
                futures = []
                for browser_id in browser_ids:
                    if mode == "1":
                        # 分发+养号
                        # 用 xhs_dispense 的自动化（分发+养号）
                        futures.append(executor.submit(open_browser_with_id_and_run_automation, browser_map, browser_id, vps_mapping))
                    elif mode == "2":
                        # 监控+分发
                        # 用 abmon_dispense 的监控+分发
                        futures.append(executor.submit(notification_monitor_worker, vps_mapping, browser_id, 10, browser_map))
                    elif mode == "3":
                        # 三合一：优先分发，其次养号，否则监控
                        # 先判断有无分发任务（xhs_dispense.check_fabu_config），否则判断有无养号配置，否则监控
                        from xhs_dispense import check_fabu_config, get_raise_config_row, run_yanghao_automation
                        browser = browser_map[browser_id]
                        try:
                            row = check_fabu_config(browser)
                            if row:
                                print(f"[三合一] 账号 {browser.get('name')} 检测到分发任务，优先分发")
                                futures.append(executor.submit(open_browser_with_id_and_run_automation, browser_map, browser_id, vps_mapping))
                                continue
                            # 养号判断
                            remark = browser.get("remark", "")
                            xhs_account = remark.split("_")[0] if "_" in remark else remark
                            raise_config = get_raise_config_row(xhs_account)
                            if raise_config:
                                print(f"[三合一] 账号 {browser.get('name')} 存在养号配置，执行养号")
                                ip_name = remark.split("_")[1] if "_" in remark else None
                                futures.append(executor.submit(run_yanghao_automation, browser_id, browser, vps_mapping, raise_config, ip_name))
                                continue
                            # 否则监控
                            print(f"[三合一] 账号 {browser.get('name')} 无分发/养号任务，执行监控")
                            futures.append(executor.submit(notification_monitor_worker, vps_mapping, browser_id, 10, browser_map))
                        except Exception as e:
                            print(f"[三合一] 账号 {browser.get('name')} 任务调度异常: {e}")
                            continue
                for future in concurrent.futures.as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        print(f"❌ 任务出错: {e}")
            print("✅ 一轮任务完成，随机30~60 秒后继续...\n")
        except Exception as e:
            print(f"💥 主循环异常：{e}")
        sleep_time = random.randint(30, 60)
        time.sleep(sleep_time)

if __name__ == "__main__":
    main() 