from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from bit_api import *
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import xhs_dispense
import concurrent.futures
import random
from urllib.parse import quote
from datetime import datetime
import re
import os
from urllib.parse import urlparse
import pyperclip
import sys


BASE_API = "https://api.open.hctalent.cn/channel/reply-form"

def update_mingdao_status(item, status="已发布", timeout=6):
    """
    修改明道表字段状态为指定值（默认 "已发布"），针对传入的单个循环项，不做重试和异常处理。
    :param item: 包含需要更新的行记录数据（单个 JSON 对象）。
    :param status: 要设置的状态值，默认值为 "已发布"。
    :param timeout: 请求超时时间（秒），默认值为 6。
    :return: API 调用结果
    """
    url = "https://api.mingdao.com/v2/open/worksheet/editRows"
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json",
    }
    
    # 提取 rowid
    row_id = item.get("rowid")
    if not row_id:
        raise ValueError("未找到有效的 rowid 数据")  # 如果 rowid 不存在，直接抛出标准异常

    # 构建请求数据
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "jzzhnrff",
        "rowIds": [row_id],  # 使用 rowid
        "controls": [
            {
                "controlId": "release_status",
                "value": status,
                "valueType": 1
            }
        ]
    }

    # 发起 POST 请求，设置超时
    try:
        response = requests.post(url, headers=headers, json=data, timeout=timeout)
        response.raise_for_status()  # 如果响应错误，抛出 HTTP 异常
    except requests.exceptions.RequestException as e:
        raise e  # 直接抛出请求异常，外部调用会处理

    # 检查响应状态码
    res = response.json()
    if res.get("success"):
        print(f"行记录 {row_id} 的状态已更新为: {status}")
        return res
    else:
        # 如果 API 返回失败，直接抛出异常
        error_message = res.get("error", "未知错误")
        raise ValueError(f"更新失败: {error_message}")


def get_filename_from_url(url):
    """
    从 URL 中提取文件名，并去掉查询参数部分。
    :param url: 文件的下载 URL
    :return: 清理后的文件名
    """
    parsed_url = urlparse(url)
    file_name = os.path.basename(parsed_url.path)  # 提取 URL 中的文件名部分
    return file_name.split('?')[0]  # 去掉查询参数部分


def get_media_type(file_name):
    """
    根据文件扩展名判断文件类型（图片或视频）。
    :param file_name: 文件名
    :return: 媒体类型 ('image' 或 'video')
    """
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
    
    _, ext = os.path.splitext(file_name.lower())
    
    if ext in image_extensions:
        return 'image'
    elif ext in video_extensions:
        return 'video'
    else:
        return 'unknown'  # 如果是未知类型
    

def clean_filename(filename):
    """
    清理文件名中的非法字符（包括 ?、=、&、: 等），确保在 Windows 操作系统中合法。
    :param filename: 原始文件名
    :return: 清理后的合法文件名
    """
    return re.sub(r'[\\/*?:"<>|]', "_", filename)

def download_media(tp_sp):
    """下载图片或视频并返回本地路径列表"""
    if isinstance(tp_sp, str):
        tp_sp = json.loads(tp_sp)

    if not isinstance(tp_sp, list) or not tp_sp:
        print("未找到有效的 tp_sp 数据")
        raise ValueError("未找到有效的 tp_sp 数据")
    
    media_paths = []
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    fastdown_folder = os.path.join(desktop_path, "fastdown")
    os.makedirs(fastdown_folder, exist_ok=True)
    
    for media_info in tp_sp:
        download_url = media_info.get("DownloadUrl")
        if not download_url:
            print("未找到有效的文件下载 URL")
            raise ValueError("未找到有效的文件下载 URL")
        
        response = requests.get(download_url, stream=True, timeout=30)
        if response.status_code != 200:
            raise Exception(f"文件下载失败，状态码: {response.status_code}")
        
        file_name = clean_filename(get_filename_from_url(download_url))
        local_file_path = os.path.join(fastdown_folder, file_name)
        
        with open(local_file_path, "wb") as file:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    file.write(chunk)
        
        print(f"文件已保存到本地路径: {local_file_path}")
        media_paths.append(local_file_path)
    
    return media_paths

def clean_message_content(content):
    """
    清洗评论内容，去除表情符号、换行、空格等干扰字符
    """
    if not content:
        return ""
    # 去除类似 [偷笑R] 这种小红书表情
    cleaned = re.sub(r'\[.*?R\]', '', content)
    # 去掉换行和前后空格
    cleaned = cleaned.replace('\n', '').replace('\r', '').strip()
    return cleaned

def send_reply(driver, target_message, reply_content):
    """
    在通知页匹配评论内容并自动回复
    """
    try:
        # 1. 进入通知页
        driver.get("https://www.xiaohongshu.com/notification")
        time.sleep(2)

        # 2. 点击"评论和@"Tab
        driver.find_element(By.XPATH, '//span[text()="评论和@"]').click()
        time.sleep(2)

        # 3. 遍历评论块，找到匹配的内容
        containers = driver.find_elements(By.CSS_SELECTOR, 'div.container')
        for container in containers:
            try:
                content_el = container.find_element(By.CSS_SELECTOR, '.interaction-content')
                
                cleaned_target = clean_message_content(target_message)
                cleaned_actual = clean_message_content(content_el.text)

                if cleaned_target in cleaned_actual:
                    print(f"匹配评论成功：{content_el.text}")

                    # 点击"回复"按钮
                    reply_btn = container.find_element(By.CSS_SELECTOR, '.action-reply')
                    driver.execute_script("arguments[0].click();", reply_btn)
                    time.sleep(1)

                    pyperclip.copy(reply_content)
                    textarea = driver.find_element(By.CSS_SELECTOR, 'textarea.comment-input')
                    textarea.click()
                    time.sleep(2)
                    actions = ActionChains(driver)
                    actions.key_down(Keys.CONTROL).send_keys('v').key_up(Keys.CONTROL).perform()
                    time.sleep(1)

                    send_btn = driver.find_element(By.CSS_SELECTOR, 'button.submit')
                    driver.execute_script("arguments[0].click();", send_btn)
                    print("[已发送] 自动回复成功")
                    return True

            except Exception as inner_e:
                continue
        print("未匹配到指定评论内容")
        return False
    except Exception as e:
        print(f"自动回复失败：{e}")
        return False

def handle_pending_reply(driver, monitor_xhs_number):
    """
    查询是否有待回复记录，批量执行回复逻辑，并标记为已回复
    """
    try:
        # 1. 调用接口获取需要回复的记录（改为 POST JSON）
        resp = requests.post(
            f"{BASE_API}/pending-replies",
            json={
                "monitorXhsNumber": monitor_xhs_number,
                "actionType": "comment"
            }
        )
        resp.raise_for_status()
        resp_json = resp.json()
        print(f"待回复记录返回：{resp_json}")
        data = resp_json.get("data")
    except Exception as e:
        print(f"获取待回复记录失败：{e}")
        return

    if not data:
        print("无待回复记录")
        return

    wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=073c8357-e2b2-45cb-b0bd-e04ce27e3e61"
    # wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77e0cc90-98f3-4b03-a4f3-9ee335c14c07"

    for reply_item in data:
        try:
            message_content = reply_item.get("messageContent")
            user_xhs_number = reply_item.get("userXhsNumber")
            reply_content = reply_item.get("replyContent")
            record_id = reply_item.get("id")
            print(f"准备回复用户：{user_xhs_number}，内容：{reply_content}")

            # 执行自动化回复逻辑
            replied = send_reply(driver, message_content, reply_content)

            if replied:
                print(f"记录 {record_id} 回复成功")

                # 回复成功后，标记为已回复
                try:
                    resp = requests.post(
                        f"{BASE_API}/mark-replied",
                        json={"id": record_id}
                    )
                    resp.raise_for_status()
                    print(f"记录 {record_id} 已标记为已回复")

                    # 成功时通知
                    message = (
                        f"✅ 自动回复成功\n"
                        f"> 用户号：{user_xhs_number}\n"
                        f"> 原评论：{message_content}\n"
                        f"> 回复内容：{reply_content}"
                    )
                    send_to_wechat_group(wechat_webhook_url, message)

                except Exception as e:
                    print(f"标记为已回复失败：{e}")

            else:
                print(f"记录 {record_id} 未找到匹配评论，未进行回复")

        except Exception as e:
            print(f"处理记录 {record_id} 出错：{e}")


def fetch_qw_phone(monitor_xhs_number: str) -> str:
    url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    payload = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "account_config",
        "filters": [
            {
                "controlId": "id",
                "dataType": 2,
                "spliceType": 1,
                "filterType": 1,
                "value": monitor_xhs_number
            }
        ]
    }

    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        data = response.json()
        if data.get("success") and data.get("data", {}).get("rows"):
            row = data["data"]["rows"][0]
            return row.get("qw_phone", "")  # 只返回手机号
    except Exception as e:
        print(f"获取企业微信手机号失败：{e}")
    return ""



# 全局缓存access_token
wechat_token_cache = {
    "access_token": None,
    "expires_at": 0
}

def get_userid_by_mobile_from_corp(corpid: str, corpsecret: str, mobile: str) -> str:
    def get_access_token(corpid: str, corpsecret: str) -> str:
        now = time.time()
        if wechat_token_cache["access_token"] and now < wechat_token_cache["expires_at"] - 100:
            return wechat_token_cache["access_token"]

        url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
        response = requests.get(url)
        data = response.json()

        if data.get("errcode") == 0:
            access_token = data["access_token"]
            expires_in = data["expires_in"]
            wechat_token_cache["access_token"] = access_token
            wechat_token_cache["expires_at"] = now + expires_in
            print("✅ 成功获取 access_token")
            return access_token
        else:
            raise Exception(f"❌ 获取 access_token 失败: {data}")

    def get_userid_by_mobile(access_token: str, mobile: str) -> str:
        url = f"https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token={access_token}"
        payload = {"mobile": mobile}
        response = requests.post(url, json=payload)
        data = response.json()

        if data.get("errcode") == 0:
            print(f"✅ 成功获取 userid：{data['userid']}")
            return data["userid"]
        else:
            raise Exception(f"❌ 获取 userid 失败: {data}")

    access_token = get_access_token(corpid, corpsecret)
    return get_userid_by_mobile(access_token, mobile)


def send_to_wechat_group_with_at_userid(webhook_url: str, message: str, mentioned_userids: list = None):
    mentioned_userids = mentioned_userids or []
    # 拼接 @userid markdown 语法
    mention_text = ""
    if mentioned_userids:
        mention_text = " ".join([f"<@{userid}>" for userid in mentioned_userids]) + "\n\n"

    payload = {
        "msgtype": "markdown",
        "markdown": {
            "content": mention_text + message,
            "mentioned_list": mentioned_userids  # 这里用userid列表
        }
    }

    try:
        response = requests.post(webhook_url, json=payload)
        if response.status_code == 200:
            print("✅ 企业微信通知发送成功")
        else:
            print(f"❌ 企业微信通知发送失败，状态码：{response.status_code}，响应内容：{response.text}")
    except Exception as e:
        print(f"❌ 发送企业微信通知时出错：{e}")



def analyze_comment(comment_content):
    """
    调用 GPT API 对评论内容进行分析，返回分析结果
    """
    api_url = "https://gpt.hctalent.cn/api/v1/chat/completions"
    headers = {
        "Authorization": "Bearer fastgpt-yAN1ImxLQNY6sMWXVh8qYWxPgdUevYX4hNDhgBvw5dEyimQtH8IOZNn1cNZamf1qm",
        "Content-Type": "application/json"
    }
    request_body = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": comment_content
                    }
                ]
            }
        ]
    }

    try:
        response = requests.post(api_url, headers=headers, json=request_body)
        response.raise_for_status()
        gpt_result = response.json()

        gpt_content = gpt_result['choices'][0]['message']['content']
        # 去掉 markdown 格式的代码块符号
        cleaned_content = re.sub(r"^```json\s*|```$", "", gpt_content.strip(), flags=re.MULTILINE).strip()

        gpt_data = json.loads(cleaned_content)

        return gpt_data

    except Exception as e:
        print(f"调用分析接口失败: {e}")
        return None


def process_notification(driver, browser_id, browser_map, max_user_count = 10):
    driver.get("https://www.xiaohongshu.com/explore")
    time.sleep(3)
    
    browser = browser_map[browser_id]

    monitor_username = browser.get("name")
    
    # 从备注中提取小红书号（兼容备注中包含“_”的情况）
    remark = browser.get("remark")
    if remark and "_" in remark:
        monitor_xhs_number = remark.split("_")[0]
    else:
        monitor_xhs_number = remark

    # Step 1: 首页通知红点
    try:
        notification_entry = driver.find_element(
            By.XPATH, "//a[contains(@href, '/notification')]//div[contains(@class, 'count')]"
        )
        total_badge = int(notification_entry.text.strip())
        if total_badge > 0:
            print(f"首页通知红点数量：{total_badge}，点击进入通知页")
            link_element = notification_entry.find_element(By.XPATH, "./ancestor::a[1]")
            link_element.click()
            time.sleep(2)
        else:
            print("首页通知红点为 0，跳过处理")
            return
    except Exception:
        print("首页通知没有红点，处理回复")
        # 处理评论回复
        handle_pending_reply(driver, monitor_xhs_number)
        return
    
    # Step 2: 获取通知页 Tab 红点信息
    tab_items = driver.find_elements(By.CLASS_NAME, "reds-tab-item")
    tab_counts = {}
    for tab in tab_items:
        try:
            label = tab.find_element(By.XPATH, ".//span").text.strip()
            count_el = tab.find_element(By.CLASS_NAME, "count")
            count = int(count_el.text.strip())
            tab_counts[label] = count
        except Exception:
            continue

    tab_badge_sum = sum(tab_counts.values())
    print(f"通知页所有 tab 红点总数：{tab_badge_sum}")

    current_tab_badge = max(0, total_badge - tab_badge_sum)
    print(f"当前 tab 红点数（预计要处理的数据量）：{current_tab_badge}")
    if current_tab_badge == 0:
        print("当前 tab 没有可处理的红点数据，跳过")
        return

    # current_tab_badge = 1
    # Step 3: 判断当前是哪个 Tab

    try:
        active_tab = driver.find_element(By.CSS_SELECTOR, ".reds-tab-item.active")
        active_tab_label = active_tab.find_element(By.TAG_NAME, "span").text.strip()
        print(f"当前选中 Tab：{active_tab_label}")
        # 判断当前 tab 是否为支持的类型（只处理"评论和@"和"新增关注"）
        supported_tabs = ["评论和@", "新增关注"]
        if active_tab_label not in supported_tabs:
            print(f"当前 tab【{active_tab_label}】不是支持的类型，跳过处理")
            return
    except Exception as e:
        print(f"找不到当前选中的 tab，错误：{e}")
        return

    # 2. 找通知卡片
    notification_cards = driver.find_elements(By.CSS_SELECTOR, ".container")
    print(f"共找到通知卡片：{len(notification_cards)} 条")
    processed = 0

    for card in notification_cards:
        if processed >= min(current_tab_badge, max_user_count):
            break
        try:
            user_link = card.find_element(By.CLASS_NAME, "user-avatar")
            user_href = user_link.get_attribute("href")

            user_info = card.find_element(By.CLASS_NAME, "user-info")
            user_name = user_info.find_element(By.TAG_NAME, "a").text.strip()

            interaction_type = "unknown"
            payload = {}

            if active_tab_label == "评论和@":
                try:
                    # 提取用户昵称
                    user_name = card.find_element(By.CSS_SELECTOR, ".user-info a").text.strip()

                    # 提取评论内容
                    comment_content = card.find_element(By.CLASS_NAME, "interaction-content").text.strip()

                    interaction_type = "comment"
                    payload["user"] = user_name
                    payload["comment"] = comment_content

                    print(f"\n用户昵称：{user_name}")
                    print(f"评论内容：{comment_content}")

                    # 调用分析函数
                    gpt_data = analyze_comment(comment_content)

                    if gpt_data:
                        analysis = gpt_data.get('analysis', {})
                        
                        # 情感分析
                        sentiment = analysis.get('sentiment', {})
                        sentiment_type = sentiment.get('type', '未知')
                        sentiment_confidence = sentiment.get('confidence_score', '未知')

                        # 负面评论处理建议
                        negative_details = analysis.get('negative_comment_details', {})
                        suggestion = negative_details.get('suggestion_for_deletion')
                        reason = negative_details.get('reason_for_suggestion')

                        # 交易意图识别
                        transaction = analysis.get('transaction_intent', {})
                        has_intent = transaction.get('has_intent', '否')
                        intent_type = transaction.get('intent_type') or '无'
                        specific_query = transaction.get('specific_query_or_product') or '无'
                        intent_score = transaction.get('intent_strength_score') or '无'

                        # 构建分析结果字符串
                        analysis_result = (
                            f"\n### 评论分析结果\n"
                            f"- 情感倾向：**{sentiment_type}** (置信度: {sentiment_confidence})\n"
                        )

                        if suggestion and reason:
                            analysis_result += (
                                f"- 处理建议：**{suggestion}**\n"
                                f"- 建议原因：{reason}\n"
                            )
                        else:
                            analysis_result += "- 无需处理，未检测到明显负面内容\n"

                        analysis_result += (
                            f"- 是否包含交易意图：**{has_intent}**\n"
                            f"- 意图类型：{intent_type}\n"
                            f"- 具体内容：{specific_query}\n"
                            f"- 意图强度：{intent_score}\n"
                        )

                    else:
                        analysis_result = "\n### 评论分析结果\n- 无法获取分析结果\n"
                except Exception as e:
                    print(f"处理评论和@时出错：{e}")
                    continue

            elif active_tab_label == "新增关注":
                try:
                    # 提取关注提示信息
                    follow_hint = card.find_element(By.CLASS_NAME, "interaction-hint").text
                    if "关注" not in follow_hint:
                        continue

                    # 提取用户昵称
                    user_name = card.find_element(By.CSS_SELECTOR, ".user-info a").text.strip()

                    interaction_type = "follow"
                    payload["user"] = user_name

                    print(f"\n新增关注用户昵称：{user_name}")
                except Exception as e:
                    print(f"处理新增关注时出错：{e}")
                    continue

            # 打开用户主页获取小红书号
            driver.execute_script("window.open(arguments[0]);", user_href)
            driver.switch_to.window(driver.window_handles[-1])
            time.sleep(2)
            try:
                red_id_el = driver.find_element(By.CLASS_NAME, "user-redId")
                red_id_text = red_id_el.text.strip()
                red_id = red_id_text.replace("小红书号：", "").strip()
            except Exception:
                red_id = "无小红书号"
            driver.close()
            driver.switch_to.window(driver.window_handles[0])
            time.sleep(1)

            print(f"用户小红书号：{red_id}")

            # 构建 payload 数据
            payload = {
                "monitorXhsNumber": monitor_xhs_number,   # 监控小红书号
                "monitorUsername": "",                    # 可空
                "userXhsNumber": red_id,         # 目标用户的小红书号
                "userUsername": "",              # 目标用户昵称user_name
                "actionType": interaction_type,           # "comment" / "follow"
                "messageContent": comment_content if interaction_type == "comment" else "",
                "status": 0
            }

            # 发起 POST 请求
            try:
                response = requests.post("https://api.open.hctalent.cn/channel/xhsUserActions/save", json=payload)
                print(f"入库结果：{response.status_code} - {response.text}")
            except Exception as e:
                print(f"调用接口入库失败：{e}")


            # 入库完成处理企微通知
            wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=073c8357-e2b2-45cb-b0bd-e04ce27e3e61"
            # wechat_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77e0cc90-98f3-4b03-a4f3-9ee335c14c07"

            # interaction_type 转换成中文动作
            interaction_type_map = {
                "comment": "新增评论",
                "follow": "新增关注",
                "private_message": "私信提醒",
            }

            # 获取中文动作
            action_text = interaction_type_map.get(interaction_type, "未知行为")

            comment_line = ""
            if interaction_type == "comment":
                # 点击底部预览图，打开弹窗
                preview_image = card.find_element(By.CSS_SELECTOR, ".extra-image")
                preview_image.click()
                time.sleep(2)  # 等待弹窗加载

                note_link = driver.current_url
                print(f"📎 笔记详情链接: {note_link}")
                ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                time.sleep(1)

                # 构建评论回复链接
                reply_url = (
                    f"https://api.open.hctalent.cn/reply-form/"
                    f"?user={quote(red_id)}&content={quote(comment_content)}"
                )
                comment_line = (
                    f"> 评论内容：{comment_content}\n\n"
                    f"{analysis_result}"
                    f"[点击回复这条评论]({reply_url})\n\n"
                    f"[点击查看笔记详情]({note_link})"
                )

            message = (
                f"### 发现新的用户行为\n"
                f"- 类型：**{action_text}**\n"
                f"- 监控账号昵称：**{monitor_username}**\n"
                f"- 用户昵称：**{user_name}**\n"
                f"- 用户小红书号：**{red_id}**\n"
                f"{comment_line}"
            )

            # 发送到企业微信群
            # 获取企业微信提醒人手机号
            # mentioned_phone = fetch_qw_phone(monitor_xhs_number)
            # userid = get_userid_by_mobile_from_corp("ww066d99826a459f4b", "HJAyqAQ5Eg1CLktLCzG1CF8ddrmCaHQ1gyBVX-DN9Z0", mentioned_phone)
            userid = fetch_qw_phone(monitor_xhs_number)
            send_to_wechat_group(wechat_webhook_url, message, userid)
            processed += 1

        except Exception as e:
            print(f"处理第 {processed+1} 条数据时异常：{e}")
            continue

    print(f"\n已成功处理 {processed} 条 {active_tab_label} 数据")


def send_to_wechat_group(webhook_url: str, message: str, userid: str = None):
    # 如果有 userid，前面加 @
    if userid:
        content = f"<@{userid}>\n{message}"
    else:
        content = message

    payload = {
        "msgtype": "markdown",
        "markdown": {
            "content": content
        }
    }

    try:
        response = requests.post(webhook_url, json=payload)
        if response.status_code == 200:
            print("✅ 企业微信通知发送成功")
        else:
            print(f"❌ 企业微信通知发送失败，状态码：{response.status_code}，响应内容：{response.text}")
    except Exception as e:
        print(f"❌ 发送企业微信通知时出错：{e}")


def fetch_vps_mapping():
    """获取 VPS 映射信息"""
    url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    payload = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "vps_mapping",
        "filters": []
    }
    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        data = response.json()
        if data.get("success") and data.get("data", {}).get("rows"):
            return {row["ip_name"]: row for row in data["data"]["rows"]}
    except Exception as e:
        print(f"获取 VPS 映射失败：{e}")
    return {}

def get_ppp0_ip(info):
    """获取 VPS 的 ppp0 接口 IP"""
    try:
        ip = info.get("ppp0_ip", "").strip()
        if ip:
            return ip
    except Exception as e:
        print(f"获取 ppp0 IP 失败：{e}")
    return ""

def get_existing_browser_map():
    """获取当前所有浏览器的完整信息映射"""
    browser_map = {}
    browser_data = get_browser_list(page=0, page_size=100)

    if not browser_data or not browser_data['success']:
        print("获取浏览器列表失败，程序退出。")
        time.sleep(10)
        return browser_map

    for browser in browser_data['data']['list']:
        browser_id = browser['id']
        browser_map[browser_id] = browser  # 直接存整个 browser 对象

    return browser_map

def create_missing_browsers(account_configs):
    """创建未创建的浏览器，返回 browser_id -> ip 映射"""
    browser_ip_map = {}

    for account in account_configs:
        bite_window_status = account.get('bite_window_status', '')

        # ip_address = extract_ip_from_account(account)

        if bite_window_status == '未创建':
            browser_id = createBrowserNoProxy(account)
            if browser_id:
                update_browser_status(account, browser_id, "创建成功")

    return browser_ip_map

def fetch_logged_in_browser_ids(account_configs, browser_map):
    """获取已登录的浏览器id"""
    browser_ids = []

    for account in account_configs:
        browser_id = account.get("bite_window_id")
        if not browser_id:
            continue

        # 只有当 bite_window_status 为 "已登录" 时，才添加到 browser_ids
        if account.get("bite_window_status") == "已登录":
            browser_ids.append(browser_id)

    return browser_ids

# def detect_and_update_browser_ips(account_configs, browser_map, vps_mapping):
#     """检测并更新浏览器 IP"""
#     for account in account_configs:
#         browser_id = account.get('bite_window_id')
#         if browser_id and browser_id in browser_map:
#             ip_name = account.get('ip_name')
#             if ip_name in vps_mapping:
#                 vps_info = vps_mapping[ip_name]
#                 current_ip = get_ppp0_ip(vps_info)
#                 if current_ip:
#                     try:
#                         update_browser_ip(browser_id, current_ip)
#                         print(f"✅ 已更新浏览器 {browser_id} 的 IP 为 {current_ip}")
#                     except Exception as e:
#                         print(f"❌ 更新浏览器 IP 失败：{e}")


def check_fabu_config(browser):
    """检查是否有符合条件的发布内容"""
    full_remark = browser.get("remark", "")
    account_number = full_remark.split("_")[0].strip()
    # print(f"xxxxxxxxxxx:{account_number}")
    name = browser.get("name", "")
    print(f"检查 {name} 的待发布内容...")
    time.sleep(random.randint(5, 10))
    url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    headers = {"Content-Type": "application/json"}
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "jzzhnrff",
        "pageSize": 1000,
        "pageIndex": 1,
        "controls": [],
        "filters": [
            {"controlId": "release_status", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "未发布"},
            {"controlId": "channel_type", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "小红书"}
        ]
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            res = response.json()
            if res.get("success") and "rows" in res.get("data", {}):
                rows = res["data"]["rows"]
                for row in rows:
                    account_data_str = row.get("account", "")
                    release_time = row.get("release_time")
                    account_data = json.loads(account_data_str)
                    if isinstance(account_data, list):
                        for account_item in account_data:
                            source_value = json.loads(account_item.get("sourcevalue", "{}"))
                            account_in_data = source_value.get("66d7fffe98435d4ec600ca08", "")
                            if account_in_data.strip().lower() == account_number.strip().lower():
                                release_time_obj = datetime.strptime(release_time, "%Y-%m-%d %H:%M:%S")
                                if datetime.now() >= release_time_obj:
                                    return row  # 直接返回符合条件的发布记录
    except Exception as e:
        print(f"请求出错: {e}")
    return None


def notification_monitor_worker(browser_id, max_user_count, browser_map):
    try:
        browser = browser_map[browser_id]
        name = browser.get("name")

        res = openBrowser(browser_id)
        driver_path = res['data']['driver']
        debugger_address = res['data']['http']

        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("debuggerAddress", debugger_address)

        chrome_service = Service(driver_path)
        driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

        try:
            # 1. 先执行发布任务
            # ✅ 优先检查是否有发布任务
            row = check_fabu_config(browser)
            if row:
                print(f"\n===== {name} 正在执行发布任务 =====")
                run_automation(driver, row, browser_id)
            
            # 2. 然后执行监控任务
            print(f"\n===== {name} 正在处理通知 =====")
            process_notification(driver, browser_id, browser_map, max_user_count=max_user_count)
            
        except Exception as e:
            print(f"❌ 浏览器 {name} 任务异常：{e}")
        finally:
            driver.quit()
            print(f"🛑 浏览器 {name} 任务完成，已关闭")

    except Exception as e:
        print(f"❌ 启动浏览器 {name} 失败：{e}")



def run_automation(driver, row, browser_id):
    """执行小红书自动化发布（支持图文 & 视频 + 视频封面）"""
    title = row.get("title", "默认标题")
    zhengwen = row.get("zhengwen", "默认正文")
    tp_sp = row.get("tp_sp", [])  # 可能包含图片或视频
    video_cover = row.get("video_cover", [])  # 视频封面（可选）

    if isinstance(video_cover, str):
        try:
            video_cover = json.loads(video_cover)  # 解析 JSON 字符串
        except json.JSONDecodeError:
            video_cover = []  # 解析失败就置为空列表
    try:
        media_paths = download_media(tp_sp)  # 下载所有图片 & 视频
        if video_cover:
            print("进入封面下载")
            cover_path = download_media(video_cover)
    except Exception as e:
        print(f"媒体下载失败: {e}")
        return

    print("执行自动化发布...")
    driver.get('https://creator.xiaohongshu.com/publish/publish?source=official')

    if not media_paths:
        print("未找到媒体文件，取消发布")
        return

    # **判断是否有视频**
    has_video = any(get_media_type(path) == "video" for path in media_paths)

    if has_video:
        # **处理视频发布**
        # 定位到 "上传视频" 标签
        upload_video_tab = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//*[text()='上传视频']"))
        )
        upload_video_tab.click()
        time.sleep(2) 

        upload_btn = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, '//div[contains(@class, "creator-tab")]/span[text()="上传视频"]'))
        )
        upload_btn.click()

        # 只上传第一个视频
        video_path = next(path for path in media_paths if get_media_type(path) == "video")
        upload_input = driver.find_element(By.XPATH, '//input[@type="file" and contains(@class, "upload-input")]')
        upload_input.send_keys(video_path)
        time.sleep(8)

        # 等待“视频解析中”进度条消失
        WebDriverWait(driver, 600).until(
            EC.invisibility_of_element_located((By.XPATH, '//div[contains(text(), "视频解析中")]'))
        )
        print("视频解析完成")

        # 等待“上传中”进度条消失
        WebDriverWait(driver, 600).until(
            EC.invisibility_of_element_located((By.XPATH, '//div[contains(text(), "上传中")]'))
        )
        print("视频上传完成")

        """处理封面上传"""
        try:
            # 上传封面文件
            if cover_path:
                print(cover_path)
                # 点击“设置封面”按钮
                set_cover_btn = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//div[contains(@class, "uploadCover")]'))
                )
                set_cover_btn.click()
                time.sleep(2)
                # 等待 file input 出现
                upload_input = WebDriverWait(driver, 300).until(
                    EC.presence_of_element_located((
                        By.XPATH, '//input[@type="file" and @accept="image/png, image/jpeg, image/*"]'
                    ))
                )

                # 直接传入文件路径上传
                path_to_upload = cover_path[0].replace("\\", "\\\\")
                upload_input.send_keys(path_to_upload)

                time.sleep(3)
                # 等待弹窗出现
                modal = WebDriverWait(driver, 120).until(
                    EC.visibility_of_element_located((By.CLASS_NAME, "d-modal"))
                )

                # 在弹窗里等待“确定”按钮可点击
                confirm_button = WebDriverWait(modal, 120).until(
                    EC.element_to_be_clickable((By.XPATH, ".//button[contains(@class, 'mojito-button') and contains(., '确定')]"))
                )

                confirm_button.click()

                # 监控 `loading` 元素的 `style`，等待它变成 `display: none;`，说明封面上传成功
                WebDriverWait(driver, 120).until(
                    lambda d: 'display: none' in d.find_element(By.XPATH, '//div[contains(@class, "loading")]').get_attribute("style")
                )

                # 等待弹窗彻底关闭
                WebDriverWait(driver, 120).until(
                    EC.invisibility_of_element_located((By.CLASS_NAME, "d-modal"))
                )
                print("弹窗已关闭！")
                print("封面上传完成！")
            else:
                print("封面路径为空，跳过上传")

        except Exception as e:
            print(f"封面选择失败: {e}")

    else:
        # **处理图文发布**
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '.creator-tab'))
        )
        tabs = driver.find_elements(By.CSS_SELECTOR, '.creator-tab')

        for i, tab in enumerate(tabs):
            try:
                title_el = tab.find_element(By.CSS_SELECTOR, 'span.title')
                title_btn = title_el.text.strip()

                if title_btn == "上传图文" and tab.is_displayed():
                    tab.click()
                    break
            except Exception as e:
                print(f"点击第 {i+1} 个上传图文时异常：{e}")

        upload_input = driver.find_element(By.XPATH, '//input[@type="file" and contains(@class, "upload-input")]')
        upload_input.send_keys("\n".join(media_paths))  # 上传多张图片
        time.sleep(8)

        # 等待所有进度条消失
        WebDriverWait(driver, 600).until(
            EC.invisibility_of_element_located((By.XPATH, '//div[contains(@class, "center")]/div[contains(@class, "inner")]'))
        )

        # 依次等待每个上传的进度条消失
        # for i in range(len(media_paths)):
        #     WebDriverWait(driver, 60).until(
        #         EC.invisibility_of_element_located((By.XPATH, '//div[contains(@class, "center")]/div[contains(@class, "inner")]'))
        #     )
        #     print(f"第 {i+1} 张图片上传完成")

        print("所有图片上传完成，继续执行后续流程")
        time.sleep(2)

    # **填写标题、正文**
    time.sleep(2)
    # 复制标题到剪贴板
    pyperclip.copy(title)
    title_input = WebDriverWait(driver, 200).until(EC.presence_of_element_located((By.XPATH, '//input[@class="d-text" and @type="text"]')))
    driver.execute_script("arguments[0].scrollIntoView();", title_input)
    # title_input.send_keys(title)
    title_input.click()  # 确保输入框获得焦点
    title_input.send_keys(Keys.CONTROL, 'v')

    # 输入正文
    # 复制文本到剪贴板
    time.sleep(2)
    pyperclip.copy(zhengwen)
    editor_input = driver.find_element(By.XPATH, '//div[@class="ql-editor ql-blank"]')
    # editor_input.send_keys(zhengwen)
    # 定位输入框并粘贴
    editor_input.click()  # 确保输入框获得焦点
    editor_input.send_keys(Keys.CONTROL, 'v')  # Windows / Linux
    time.sleep(3)

    editor_input.send_keys(Keys.ENTER)
    time.sleep(2)

    # **处理话题**
    topics = row.get("topic_word", "")
    topic_list = topics.split(",") if topics else []

    for topic in topic_list:
        if topic.strip():
            driver.execute_script("arguments[0].scrollIntoView();", editor_input)
            editor_input.send_keys(f" #{topic.strip()}")
            # 等待下拉列表显示
            try:
                WebDriverWait(driver, 5).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "#quill-mention-list"))
                )
                time.sleep(3)
                # 如果下拉列表出现，按下 ENTER 键
                editor_input.send_keys(Keys.ENTER)
                time.sleep(1)
            except:
                # 如果没有找到下拉列表，跳过按键
                print(f"没有找到下拉列表，跳过 {topic.strip()}")
                continue
            # time.sleep(3)
            # editor_input.send_keys(Keys.ENTER)
            # time.sleep(1)

    # **点击发布**
    publish_btn = driver.find_element(By.XPATH, '//div[@class="d-button-content"]//span[text()="发布"]')
    publish_btn.click()
    time.sleep(1)
    print("发布完成")

    # **更新明道状态**
    try:
        # 等待元素 "发布成功"出现，最多等待 6 秒
        WebDriverWait(driver, 6).until(
            EC.text_to_be_present_in_element((By.CSS_SELECTOR, "p.title"), "发布成功")
        )
        # **更新明道状态**
        update_mingdao_status(row)
        print("明道状态更新成功")
    except Exception as e:
        print("未检测到 '发布成功'，不更新状态和窗口")

    except Exception as e:
        print(f"❌ 出现异常：{e}")
        closeBrowser(browser_id)


def monitor_notifications_loop(max_user_count=10):
    if len(sys.argv) > 1:
        bite_user_name = sys.argv[1]
    else:
        bite_user_name = input("请输入启动用户名: ")

    while True:
        try:
            print("🔄 检查配置和浏览器状态...")
            account_configs = fetch_account_config(bite_user_name)

            # 创建未创建的浏览器
            create_missing_browsers(account_configs)

            # 2. 获取所有浏览器 ID -> 浏览器对象的完整映射
            browser_map = get_existing_browser_map()

            # 检测并更新浏览器 IP
            # detect_and_update_browser_ips(account_configs, browser_map, vps_mapping)

            # 获取已登录的浏览器 ID
            browser_ids = fetch_logged_in_browser_ids(account_configs, browser_map)

            if not browser_ids:
                print("⚠️ 没有登录的浏览器，等待 30 秒后重试")
                time.sleep(30)
                continue

            # 执行任务（发布+监控）
            print(f"🚀 启动任务，总数 {len(browser_ids)}，最大并发 {len(browser_ids)}")
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(browser_ids)) as executor:
                futures = []
                for browser_id in browser_ids:
                    future = executor.submit(notification_monitor_worker, browser_id, max_user_count, browser_map)
                    futures.append(future)
                    time.sleep(20)  # 控制每个线程的启动间隔

                for future in concurrent.futures.as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        print(f"❌ 任务出错: {e}")

            print("✅ 一轮任务完成，随机30~60 秒后继续...\n")

        except Exception as e:
            print(f"💥 主循环异常：{e}")

        sleep_time = random.randint(30, 60)
        time.sleep(sleep_time)

monitor_notifications_loop(max_user_count=10)

