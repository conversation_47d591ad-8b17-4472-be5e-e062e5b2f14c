import requests
import json
import time

# 官方文档地址
# https://doc2.bitbrowser.cn/jiekou/ben-di-fu-wu-zhi-nan.html

# 此demo仅作为参考使用，以下使用的指纹参数仅是部分参数，完整参数请参考文档

url = "http://127.0.0.1:54345"
headers = {'Content-Type': 'application/json'}

# 更新窗口状态
def update_browser_status(item, browser_id, status="创建成功", timeout=6):
    """
    修改浏览器窗口状态为指定值（默认 "创建成功"）。
    :param item: 包含需要更新的行记录数据（单个 JSON 对象）。
    :param status: 要设置的状态值，默认值为 "创建成功"。
    :param timeout: 请求超时时间（秒），默认值为 6。
    :return: API 调用结果
    """
    url = "https://api.mingdao.com/v2/open/worksheet/editRows"
    headers = {"Content-Type": "application/json"}
    
    row_id = item.get("rowid")
    if not row_id:
        raise ValueError("未找到有效的 rowid 数据")
    
    data = {
        "appKey": "f08bf7f7cfe8c038",
        "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
        "worksheetId": "account_config",
        "rowIds": [row_id],
        "controls": [
            {
                "controlId": "bite_window_status",
                "value": status,
                "valueType": 1
            },
            {
                "controlId": "bite_window_id",
                "value": browser_id,
                "valueType": 1
            }
        ]
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=timeout)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        raise e
    

# 获取明道账号配置
def fetch_account_config(bite_user_name):
    api_url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
    app_key = "f08bf7f7cfe8c038"
    sign = "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ=="
    headers = {"Content-Type": "application/json"}

    payload = {
        "appKey": app_key,
        "sign": sign,
        "worksheetId": "66d82482b11ed63a4fb450e7",
        # "pageSize":2,
        # "pageIndex":0,
        "controls": [],
        "filters": [
            {
                "controlId": "bite_user_name",          
                "filterType": 2,              
                "value": bite_user_name 
            }
        ]
    }

    try:
        response = requests.post(api_url, headers=headers, data=json.dumps(payload))
        response.raise_for_status()  # 如果请求失败，抛出异常
        
        # 假设返回的数据包含 IP 地址
        data = response.json()
        return data.get("data", {}).get("rows")
    except requests.exceptions.RequestException as e:
        print(f"请求出错: {e}")
        return []
    
def createBrowserNoProxy(account):  # 创建或者更新窗口，指纹参数 browserFingerPrint 如没有特定需求，只需要指定下内核即可，如果需要更详细的参数，请参考文档
    json_data = {
        'name': account.get('zhmc', ''),  # 窗口名称
        'remark': account.get('id', ''),  # 备注
        'syncTabs': False, # 不同步浏览器已打开的标签页面
        'proxyMethod': 2,  # 代理方式 2自定义 3 提取IP
        # 代理类型  ['noproxy', 'http', 'https', 'socks5', 'ssh']
        'proxyType': 'noproxy',
        'host': '',  # 代理主机
        'port': '',  # 代理端口
        'proxyUserName': '',  # 代理账号
        "browserFingerPrint": {  # 指纹对象
            'coreVersion': '134'  # 内核版本，注意，win7/win8/winserver 2012 已经不支持112及以上内核了，无法打开
        }
    }

    res = requests.post(f"{url}/browser/update",
                        data=json.dumps(json_data), headers=headers).json()
    browserId = res['data']['id']
    print(browserId)
    return browserId

def createBrowser(host, account, vps_info):  # 创建或者更新窗口，指纹参数 browserFingerPrint 如没有特定需求，只需要指定下内核即可，如果需要更详细的参数，请参考文档
    print(f"🔍 当前 VPS 信息: {vps_info}")
    port = vps_info.get("wsport", 65531)
    print(f"🛠️ 使用的代理端口: {port}")
    
    json_data = {
        'name': account.get('zhmc', ''),   # 窗口名称
        'syncTabs': False, # 不同步浏览器已打开的标签页面
        'remark': f"{account.get('id', '')}_{account.get('ip_name', '')}",  # 备注
        'proxyMethod': 2,  # 代理方式 2自定义 3 提取IP
        # 代理类型  ['noproxy', 'http', 'https', 'socks5', 'ssh']
        'proxyType': 'socks5',
        'host': host,  # 代理主机
        'port': vps_info.get("wsport", 65531),  # 代理端口
        'proxyUserName': 'in',  # 代理账号
        'proxyPassword': 'zheshigesocks5',
        # 'workbench': 'disable', # 不显示工作台
        # 'platform': 'https://www.xiaohongshu.com/explore', # 账号平台
        "browserFingerPrint": {  # 指纹对象
            'coreVersion': '134'  # 内核版本，注意，win7/win8/winserver 2012 已经不支持112及以上内核了，无法打开
        }
        
    }

    res = requests.post(f"{url}/browser/update",
                        data=json.dumps(json_data), headers=headers).json()
    browserId = res['data']['id']
    print(browserId)
    return browserId

def updateBrowser():  # 更新窗口，支持批量更新和按需更新，ids 传入数组，单独更新只传一个id即可，只传入需要修改的字段即可，比如修改备注，具体字段请参考文档，browserFingerPrint指纹对象不修改，则无需传入
    json_data = {'ids': ['93672cf112a044f08b653cab691216f0'],
                 'remark': '我是一个备注', 'browserFingerPrint': {}}
    res = requests.post(f"{url}/browser/update/partial",
                        data=json.dumps(json_data), headers=headers).json()
    print(res)


def get_browser_detail(browser_id):
    json_data = {"id": browser_id}
    
    res = requests.post(f"{url}/browser/detail", data=json.dumps(json_data), headers=headers)
    
    return res.json()


def openBrowser(id):  # 直接指定ID打开窗口，也可以使用 createBrowser 方法返回的ID
    json_data = {"id": f'{id}'}
    res = requests.post(f"{url}/browser/open",
                        data=json.dumps(json_data), headers=headers).json()
    return res


def closeBrowser(id):  # 关闭窗口
    json_data = {'id': f'{id}'}
    requests.post(f"{url}/browser/close",
                  data=json.dumps(json_data), headers=headers).json()


def deleteBrowser(id):  # 删除窗口
    json_data = {'id': f'{id}'}
    print(requests.post(f"{url}/browser/delete",
          data=json.dumps(json_data), headers=headers).json())


# 批量修改窗口代理信息
def update_proxy(ids, proxy_method, proxy_type, host, port, proxy_username, proxy_password):
    json_data = {
        "ids": ids,
        "proxyMethod": proxy_method,
        "proxyType": proxy_type,
        "host": host,
        "port": port,
        "proxyUserName": proxy_username,
        "proxyPassword": proxy_password
    }
    
    res = requests.post(f"{url}/browser/proxy/update", data=json.dumps(json_data), headers=headers)
    return res.json()

# 获取浏览器窗口列表
def get_browser_list(page=0, page_size=100):
    json_data = {
        "page": page,
        "pageSize": page_size
    }
    
    response = requests.post(f"{url}/browser/list", data=json.dumps(json_data), headers=headers)     
    return response.json()
    

if __name__ == '__main__':
    browser_id = createBrowser()
    openBrowser(browser_id)

    time.sleep(10)  # 等待10秒自动关闭窗口

    closeBrowser(browser_id)

    time.sleep(10)  # 等待10秒自动删掉窗口

    deleteBrowser(browser_id)
