# 小红书自动化系统说明

## 功能概述

集成了发布和统计功能的小红书自动化系统，支持内容发布、数据统计、代理管理等功能。

## 代码结构重构

### 🔧 公共函数提取
- `initialize_system()`: 系统初始化（VPS映射、账号配置、浏览器创建）
- `run_publish_tasks()`: 执行发布任务
- `run_statistics_for_all_accounts()`: 执行统计任务

## 使用方法

### 1. 安装依赖
```bash
pip install schedule
```

### 2. 启动程序
```bash
python main.py
```

### 3. 选择运行模式

启动后会提示选择运行模式：

```
🚀 请选择运行模式:
1. 发布模式 (默认)
2. 统计模式 (立即执行一次)
3. 定时统计模式 (每天0点执行)
4. 发布+定时统计模式
```

#### 模式说明：

- **模式1 - 发布模式**：原有的发布功能，不执行统计
- **模式2 - 统计模式**：立即对所有账号执行一次数据统计
- **模式3 - 定时统计模式**：每天0点自动执行统计任务
- **模式4 - 发布+定时统计模式**：同时运行发布功能和定时统计功能

## 统计功能详情

### 数据收集
- 自动访问小红书笔记管理页面
- 滚动加载所有已发布笔记
- 提取每篇笔记的数据：
  - 笔记标题
  - 浏览量（小眼睛）
  - 评论数
  - 点赞数
  - 收藏数
  - 转发数

### 数据同步
- 自动同步到明道云矩阵笔记库
- 使用现有的API配置和字段映射
- 支持批量上传

### 字段映射
明道云字段对应关系：
- `bjbt`: 笔记标题
- `nicheng`: 账号昵称
- `xhsh`: 小红书账号
- `xiaoyanjing_number`: 浏览量
- `pinglun_number`: 评论数
- `dianzan_number`: 点赞数
- `shoucang_number`: 收藏数
- `zhuanfa_number`: 转发数

## 功能改进

### 🔄 智能滚动加载
- 自动检测页面是否有新内容加载
- 支持点击"加载更多"按钮
- 连续3次无新内容自动停止
- 最大滚动100次防止无限循环

### 📊 数据提取优化
- 支持多种页面结构的笔记选择器
- 智能解析数字格式（支持1.2k、1.2万等）
- 容错处理，单个笔记失败不影响整体

### 🔄 数据去重功能
- 自动检查明道云中已存在的笔记
- 新笔记：直接添加到明道云
- 已存在笔记：更新数据而不是重复添加
- 使用"标题+账号"作为唯一标识

## 注意事项

1. **登录检查**：统计前会自动检查账号登录状态
2. **错误处理**：单个账号失败不影响其他账号
3. **定时任务**：默认每天0点执行，可按需调整
4. **浏览器管理**：自动启动和关闭浏览器，无需手动操作
5. **数据去重**：自动处理重复数据，避免明道云中出现重复记录

## 测试建议

建议先选择"模式2"测试统计功能是否正常工作，确认无误后再使用定时模式。

## 日志查看

统计过程中的详细信息会在控制台显示，包括：
- 浏览器启动状态
- 滚动加载进度
- 数据提取详情
- 去重检查结果
- 明道云同步结果（新增/更新）
- 错误信息等

## 故障排除

### 滚动加载问题
- 如果笔记数量少于预期，检查网络连接
- 页面结构变化可能需要更新选择器

### 数据提取问题
- 检查页面是否完全加载
- 确认小红书页面结构未发生重大变化

### 明道云同步问题
- 检查API配置是否正确
- 确认网络连接正常
- 查看返回的错误信息
