#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书笔记评论监听工具

功能：
- 监听多个小红书笔记的评论变化
- 发现新评论时自动发送企业微信通知
- 本地存储评论数据，避免重复通知
- 支持日志记录

使用方法：
1. 确保已安装 selenium 和 requests: pip install selenium requests
2. 确保比特浏览器正在运行，并已创建至少一个浏览器窗口
3. 配置企业微信webhook地址（修改下方WECHAT_WEBHOOK_URL）
4. 运行脚本: python xhs_comment_monitor.py
5. 按提示输入要监听的笔记链接

注意事项：
- 请遵守小红书使用条款
- 建议检查间隔不少于5分钟
- 程序会在logs/目录下生成日志文件
- 评论数据存储在comment_data/目录下
- 需要比特浏览器API服务运行在 http://127.0.0.1:54345
"""

import time
import os
import json
import requests
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from selenium.webdriver.chrome.service import Service
from urllib.parse import urlparse
import hashlib
from bit_api import openBrowser, closeBrowser, get_browser_list

# 企业微信通知配置 - 请修改为你的webhook地址
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=77e0cc90-98f3-4b03-a4f3-9ee335c14c07"

# 检查间隔（秒）
CHECK_INTERVAL = 300  # 5分钟

# 配置日志
def setup_logger():
    """设置日志记录器，按日期分离日志文件"""
    today = datetime.now().strftime('%Y-%m-%d')
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f"xhs_comment_monitor_{today}.log")
    
    logger = logging.getLogger('xhs_comment_monitor')
    logger.setLevel(logging.INFO)
    
    # 清除已有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

logger = setup_logger()

def send_to_wechat_group(webhook_url: str, message: str, userid: str = None):
    """发送企业微信群通知"""
    if userid:
        content = f"<@{userid}>\n{message}"
    else:
        content = message

    payload = {
        "msgtype": "markdown",
        "markdown": {
            "content": content
        }
    }

    try:
        response = requests.post(webhook_url, json=payload)
        if response.status_code == 200:
            logger.info("✅ 企业微信通知发送成功")
        else:
            logger.error(f"❌ 企业微信通知发送失败，状态码：{response.status_code}，响应内容：{response.text}")
    except Exception as e:
        logger.error(f"❌ 发送企业微信通知时出错：{e}")

def extract_note_id_from_url(url):
    """从小红书链接中提取笔记ID"""
    try:
        # 解析URL，提取路径中的笔记ID
        # 例如：https://www.xiaohongshu.com/explore/6864780c000000002203d7d7
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.split('/')
        
        if 'explore' in path_parts:
            explore_index = path_parts.index('explore')
            if explore_index + 1 < len(path_parts):
                note_id = path_parts[explore_index + 1]
                # 去掉可能的查询参数
                note_id = note_id.split('?')[0]
                return note_id
        
        logger.warning(f"无法从URL中提取笔记ID: {url}")
        return None
    except Exception as e:
        logger.error(f"提取笔记ID失败: {e}")
        return None

def create_driver_from_browser_id(browser_id):
    """使用比特浏览器ID创建Chrome浏览器驱动"""
    try:
        logger.info(f"正在打开比特浏览器 ID: {browser_id}")
        res = openBrowser(browser_id)

        if not res or not res.get('success'):
            logger.error(f"❌ 打开比特浏览器失败: {res}")
            return None

        driver_path = res['data']['driver']
        debugger_address = res['data']['http']

        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("debuggerAddress", debugger_address)
        chrome_service = Service(driver_path)
        driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

        logger.info("✅ 比特浏览器启动成功")
        return driver
    except Exception as e:
        logger.error(f"❌ 创建比特浏览器驱动失败: {e}")
        print(f"❌ 比特浏览器启动失败: {e}")
        return None

def get_available_browser():
    """获取可用的浏览器ID"""
    try:
        browser_data = get_browser_list(page=0, page_size=100)
        if not browser_data or not browser_data.get('success'):
            logger.error("获取浏览器列表失败")
            return None

        browsers = browser_data.get('data', {}).get('list', [])
        if not browsers:
            logger.error("没有找到可用的浏览器")
            return None

        # 返回第一个浏览器的ID
        browser_id = browsers[0]['id']
        browser_name = browsers[0].get('name', '未知')
        logger.info(f"选择浏览器: {browser_name} (ID: {browser_id})")
        return browser_id
    except Exception as e:
        logger.error(f"获取浏览器列表失败: {e}")
        return None

def get_note_comments(driver, note_url):
    """获取笔记的评论列表"""
    try:
        logger.info(f"正在访问笔记: {note_url}")
        driver.get(note_url)
        time.sleep(5)  # 等待页面加载
        
        # 等待评论区加载
        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".comments-container, .comment-container, [class*='comment']"))
            )
        except:
            logger.warning("未找到评论容器，可能没有评论或页面结构变化")
            return []
        
        comments = []
        
        # 尝试多种评论选择器
        comment_selectors = [
            ".parent-comment",
            ".comment-item",
            "[class*='comment-item']",
            ".note-comment-item"
        ]

        comment_elements = []
        for selector in comment_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    comment_elements = elements
                    logger.info(f"✅ 使用选择器 {selector} 找到 {len(elements)} 条评论")
                    break
            except:
                continue
        
        if not comment_elements:
            logger.warning("未找到评论元素")
            return []
        
        for i, comment_elem in enumerate(comment_elements[:50]):  # 限制最多获取50条评论
            try:
                # 尝试多种用户名选择器
                username_selectors = [
                    ".author .name",
                    ".user-name", 
                    ".username",
                    "[class*='user-name']",
                    "[class*='author']"
                ]
                
                username = "未知用户"
                for selector in username_selectors:
                    try:
                        username_elem = comment_elem.find_element(By.CSS_SELECTOR, selector)
                        username = username_elem.text.strip()
                        if username:
                            break
                    except:
                        continue
                
                # 尝试多种评论内容选择器
                content_selectors = [
                    ".content .note-text",
                    ".comment-content",
                    ".content",
                    "[class*='content']",
                    "[class*='text']"
                ]
                
                content = "无内容"
                for selector in content_selectors:
                    try:
                        content_elem = comment_elem.find_element(By.CSS_SELECTOR, selector)
                        content = content_elem.text.strip()
                        if content and content != username:  # 确保不是用户名
                            break
                    except:
                        continue
                
                # 尝试获取时间
                time_selectors = [
                    ".date span",
                    ".time",
                    "[class*='time']",
                    "[class*='date']"
                ]
                
                comment_time = "未知时间"
                for selector in time_selectors:
                    try:
                        time_elem = comment_elem.find_element(By.CSS_SELECTOR, selector)
                        comment_time = time_elem.text.strip()
                        if comment_time:
                            break
                    except:
                        continue
                
                # 生成评论的唯一标识 - 只基于用户名和内容，完全忽略时间
                # 清理内容，去除可能的变化字符
                clean_content = content.replace('\n', '').replace('\r', '').strip()
                clean_username = username.strip()

                # 唯一ID：只基于用户名和内容
                comment_id = hashlib.md5(f"{clean_username}_{clean_content}".encode()).hexdigest()

                # 为了兼容旧数据，保留primary_id字段，但值与comment_id相同
                primary_id = comment_id
                
                comment_data = {
                    "id": comment_id,
                    "primary_id": primary_id,  # 用于更宽松的去重
                    "username": clean_username,
                    "content": clean_content,
                    "time": comment_time,
                    "timestamp": datetime.now().isoformat()
                }
                
                comments.append(comment_data)
                logger.debug(f"获取评论 {i+1}: {username} - {content[:50]}...")
                
            except Exception as e:
                logger.warning(f"解析第 {i+1} 条评论失败: {e}")
                continue
        
        logger.info(f"成功获取 {len(comments)} 条评论")
        return comments
        
    except Exception as e:
        logger.error(f"获取笔记评论失败: {e}")
        return []

def load_previous_comments(note_id):
    """加载之前保存的评论数据"""
    try:
        data_dir = "comment_data"
        os.makedirs(data_dir, exist_ok=True)
        
        file_path = os.path.join(data_dir, f"{note_id}_comments.json")
        
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('comments', [])
        
        return []
    except Exception as e:
        logger.error(f"加载历史评论数据失败: {e}")
        return []

def save_comments(note_id, comments):
    """保存评论数据到本地文件"""
    try:
        data_dir = "comment_data"
        os.makedirs(data_dir, exist_ok=True)
        
        file_path = os.path.join(data_dir, f"{note_id}_comments.json")
        
        data = {
            "note_id": note_id,
            "last_update": datetime.now().isoformat(),
            "comment_count": len(comments),
            "comments": comments
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"评论数据已保存: {file_path}")
        
    except Exception as e:
        logger.error(f"保存评论数据失败: {e}")

def find_new_comments(old_comments, new_comments):
    """找出新增的评论 - 主要基于用户名和内容匹配，避免时间变化导致的重复"""
    # 只使用primary_id进行匹配，因为时间格式会变化
    old_primary_ids = {comment.get('primary_id', comment['id']) for comment in old_comments}

    # 为了调试，也创建用户名+内容的简单匹配
    old_user_content = {f"{comment['username']}_{comment['content']}" for comment in old_comments}

    new_comment_list = []
    duplicate_count = 0

    for comment in new_comments:
        primary_id = comment.get('primary_id', comment['id'])
        user_content = f"{comment['username']}_{comment['content']}"

        # 使用primary_id或用户名+内容进行匹配
        if primary_id not in old_primary_ids and user_content not in old_user_content:
            new_comment_list.append(comment)
        else:
            duplicate_count += 1
            logger.debug(f"跳过重复评论: {comment['username']} - {comment['content'][:30]}...")

    if duplicate_count > 0:
        logger.info(f"过滤掉 {duplicate_count} 条重复评论")

    return new_comment_list

def monitor_note_comments(note_url, browser_id, check_interval=300):
    """监听单个笔记的评论变化"""
    note_id = extract_note_id_from_url(note_url)
    if not note_id:
        logger.error(f"无法提取笔记ID，跳过监听: {note_url}")
        return

    logger.info(f"开始监听笔记 {note_id} 的评论变化")

    driver = create_driver_from_browser_id(browser_id)
    if not driver:
        logger.error("无法创建浏览器驱动，退出监听")
        return

    try:
        # 加载历史评论数据
        previous_comments = load_previous_comments(note_id)
        is_first_run = len(previous_comments) == 0
        logger.info(f"加载历史评论数据: {len(previous_comments)} 条")

        if is_first_run:
            logger.info("🔄 首次运行，将建立评论基线，不发送通知")
            print("🔄 首次运行，正在建立评论基线...")

        while True:
            try:
                # 获取当前评论
                current_comments = get_note_comments(driver, note_url)

                if not current_comments:
                    logger.warning(f"未获取到评论数据，{check_interval}秒后重试")
                    time.sleep(check_interval)
                    continue

                # 查找新评论
                new_comments = find_new_comments(previous_comments, current_comments)

                if new_comments:
                    logger.info(f"发现 {len(new_comments)} 条新评论")

                    # 首次运行时不发送通知，只建立基线
                    if is_first_run:
                        logger.info("🔄 首次运行，跳过通知发送，建立评论基线")
                        print(f"🔄 首次运行发现 {len(new_comments)} 条评论，建立基线中...")
                        is_first_run = False  # 标记首次运行完成
                    else:
                        # 发送企业微信通知
                        for comment in new_comments:
                            message = (
                                f"### 📝 小红书笔记新评论提醒\n"
                                f"- **笔记链接**: [点击查看]({note_url})\n"
                                f"- **评论用户**: {comment['username']}\n"
                                f"- **评论内容**: {comment['content']}\n"
                                f"- **评论时间**: {comment['time']}\n"
                                f"- **检测时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                            )

                            send_to_wechat_group(WECHAT_WEBHOOK_URL, message)
                            logger.info(f"📱 已发送新评论通知: {comment['username']} - {comment['content'][:30]}...")
                            print(f"📱 发现新评论: {comment['username']} - {comment['content'][:50]}...")
                elif not is_first_run:
                    print(f"✅ {datetime.now().strftime('%H:%M:%S')} - 暂无新评论")

                # 保存当前评论数据
                save_comments(note_id, current_comments)
                previous_comments = current_comments

                logger.info(f"监听完成，{check_interval}秒后进行下次检查")
                print(f"⏰ {datetime.now().strftime('%H:%M:%S')} - 监听完成，{check_interval//60}分钟后进行下次检查")
                time.sleep(check_interval)

            except KeyboardInterrupt:
                logger.info("收到中断信号，停止监听")
                break
            except Exception as e:
                logger.error(f"监听过程中发生错误: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试

    finally:
        if driver:
            driver.quit()
            closeBrowser(browser_id)
            logger.info("浏览器已关闭")

def monitor_multiple_notes(note_urls, browser_id, check_interval=300):
    """监听多个笔记的评论变化"""
    logger.info(f"开始监听 {len(note_urls)} 个笔记的评论变化")

    # 为每个笔记创建独立的监听数据
    note_data = {}
    for url in note_urls:
        note_id = extract_note_id_from_url(url)
        if note_id:
            previous_comments = load_previous_comments(note_id)
            is_first_run = len(previous_comments) == 0
            note_data[note_id] = {
                'url': url,
                'previous_comments': previous_comments,
                'is_first_run': is_first_run
            }
            logger.info(f"笔记 {note_id} 历史评论: {len(previous_comments)} 条 {'(首次运行)' if is_first_run else ''}")

    if not note_data:
        logger.error("没有有效的笔记链接，退出监听")
        return

    driver = create_driver_from_browser_id(browser_id)
    if not driver:
        logger.error("无法创建浏览器驱动，退出监听")
        return

    try:
        while True:
            try:
                for note_id, data in note_data.items():
                    note_url = data['url']
                    previous_comments = data['previous_comments']
                    is_first_run = data['is_first_run']

                    logger.info(f"检查笔记 {note_id} 的评论变化")
                    print(f"🔍 {datetime.now().strftime('%H:%M:%S')} - 检查笔记 {note_id} 的评论...")

                    # 获取当前评论
                    current_comments = get_note_comments(driver, note_url)

                    if not current_comments:
                        logger.warning(f"笔记 {note_id} 未获取到评论数据")
                        continue

                    # 查找新评论
                    new_comments = find_new_comments(previous_comments, current_comments)

                    if new_comments:
                        logger.info(f"笔记 {note_id} 发现 {len(new_comments)} 条新评论")

                        # 首次运行时不发送通知，只建立基线
                        if is_first_run:
                            logger.info(f"🔄 笔记 {note_id} 首次运行，跳过通知发送")
                            print(f"🔄 笔记 {note_id} 首次运行发现 {len(new_comments)} 条评论，建立基线中...")
                            note_data[note_id]['is_first_run'] = False  # 标记首次运行完成
                        else:
                            # 发送企业微信通知
                            for comment in new_comments:
                                message = (
                                    f"### 📝 小红书笔记新评论提醒\n"
                                    f"- **笔记ID**: {note_id}\n"
                                    f"- **笔记链接**: [点击查看]({note_url})\n"
                                    f"- **评论用户**: {comment['username']}\n"
                                    f"- **评论内容**: {comment['content']}\n"
                                    f"- **评论时间**: {comment['time']}\n"
                                    f"- **检测时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                                )

                                send_to_wechat_group(WECHAT_WEBHOOK_URL, message)
                                logger.info(f"📱 已发送新评论通知: {comment['username']} - {comment['content'][:30]}...")
                                print(f"📱 发现新评论: {comment['username']} - {comment['content'][:50]}...")
                    elif not is_first_run:
                        print(f"✅ {datetime.now().strftime('%H:%M:%S')} - 笔记 {note_id} 暂无新评论")

                    # 保存当前评论数据并更新内存中的数据
                    save_comments(note_id, current_comments)
                    note_data[note_id]['previous_comments'] = current_comments

                    # 每个笔记之间间隔一点时间，避免请求过于频繁
                    time.sleep(5)

                logger.info(f"所有笔记检查完成，{check_interval}秒后进行下次检查")
                print(f"✅ {datetime.now().strftime('%H:%M:%S')} - 所有笔记检查完成，{check_interval//60}分钟后进行下次检查")
                time.sleep(check_interval)

            except KeyboardInterrupt:
                logger.info("收到中断信号，停止监听")
                break
            except Exception as e:
                logger.error(f"监听过程中发生错误: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试

    finally:
        if driver:
            driver.quit()
            closeBrowser(browser_id)
            logger.info("浏览器已关闭")

def clear_comment_data():
    """清理历史评论数据，重置基线"""
    data_dir = "comment_data"
    if not os.path.exists(data_dir):
        print("没有找到评论数据目录")
        return

    files = [f for f in os.listdir(data_dir) if f.endswith("_comments.json")]
    if not files:
        print("没有找到评论数据文件")
        return

    print(f"找到 {len(files)} 个评论数据文件:")
    for i, file in enumerate(files):
        print(f"{i+1}. {file}")

    choice = input("\n请选择操作：\n1. 删除所有数据文件\n2. 返回主菜单\n请输入选项(1/2): ")

    if choice == "1":
        confirm = input("⚠️ 确定要删除所有评论数据吗？这将重置所有监听基线 (y/n): ")
        if confirm.lower() == "y":
            for file in files:
                try:
                    os.remove(os.path.join(data_dir, file))
                    print(f"已删除: {file}")
                except Exception as e:
                    print(f"删除 {file} 失败: {e}")
            print("✅ 所有评论数据已清理，下次运行将重新建立基线")
        else:
            print("已取消删除操作")

    input("\n按回车键返回主菜单...")

def main():
    """主函数"""
    while True:
        print("\n=== 小红书笔记评论监听工具 ===")
        print("当检测到新评论时，会自动发送企业微信通知")
        print()
        print("请选择操作：")
        print("1. 开始监听笔记评论")
        print("2. 清理历史评论数据")
        print("3. 退出程序")

        choice = input("\n请输入选项(1-3): ")

        if choice == "1":
            # 开始监听
            # 获取可用的浏览器
            print("\n🔍 正在获取可用的比特浏览器...")
            browser_id = get_available_browser()
            if not browser_id:
                print("❌ 没有找到可用的比特浏览器，请先在比特浏览器中创建浏览器窗口")
                input("按回车键返回主菜单...")
                continue

            print(f"✅ 将使用浏览器 ID: {browser_id}")
            print()

            # 示例链接
            example_url = "https://www.xiaohongshu.com/explore/6864780c000000002203d7d7?xsec_token=ABmFn1wyulrafXzXZk26M5ARyHq7Oxphhr2d_lzS4QKGk=&xsec_source=pc_feed"
            print(f"示例链接: {example_url}")
            print()
            print("请输入要监听的小红书笔记链接（每行一个，输入空行结束）：")

            note_urls = []
            while True:
                url = input("笔记链接: ").strip()
                if not url:
                    break
                if 'xiaohongshu.com' in url and 'explore' in url:
                    note_urls.append(url)
                    print(f"✅ 已添加: {url}")
                else:
                    print("❌ 无效的小红书笔记链接，请重新输入")

            if not note_urls:
                print("没有输入有效的笔记链接，返回主菜单")
                continue

            print(f"\n📝 将监听 {len(note_urls)} 个笔记的评论变化")
            print(f"⏰ 检查间隔: {CHECK_INTERVAL} 秒 ({CHECK_INTERVAL//60} 分钟)")
            print(f"📱 企业微信通知: 已配置")
            print(f"🌐 使用浏览器: {browser_id}")
            print()
            print("💡 说明:")
            print("   - 首次运行会建立评论基线，不会发送通知")
            print("   - 后续运行只会通知真正的新评论")
            print("   - 评论数据保存在 comment_data/ 目录")
            print("   - 日志文件保存在 logs/ 目录")
            print("   - 按Ctrl+C可以中断监听")
            print()

            input("按回车键开始监听...")

            try:
                if len(note_urls) == 1:
                    monitor_note_comments(note_urls[0], browser_id, CHECK_INTERVAL)
                else:
                    monitor_multiple_notes(note_urls, browser_id, CHECK_INTERVAL)
            except KeyboardInterrupt:
                print("\n监听已中断，返回主菜单...")
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")
                input("按回车键返回主菜单...")

        elif choice == "2":
            # 清理历史数据
            clear_comment_data()
            continue
        elif choice == "3":
            # 退出程序
            print("感谢使用，再见！")
            break
        else:
            print("无效选项，请重新选择")
            continue

if __name__ == "__main__":
    main()
