import time
import datetime
import concurrent.futures
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from bit_api import *
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import re
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import random

# 企业微信通知配置
WECHAT_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b9475b2e-4c9a-42a5-abbd-5072ea1fea08"

# 登录检查配置
LOGIN_CHECK_INTERVAL = 10  # 每10次操作检查一次登录状态
AUTO_LOGIN_TIMEOUT = 60   # 自动登录等待超时时间（秒）
MAX_LOGIN_ATTEMPTS = 3    # 最大登录尝试次数

def check_login_status(driver):
    """检测登录状态 - 检查是否有login-container类或登录相关元素"""
    try:
        # 方法1：检查是否有登录容器
        login_containers = driver.find_elements(By.CLASS_NAME, "login-container")
        if login_containers:
            for container in login_containers:
                if container.is_displayed():
                    return False  # 发现登录页面，表示未登录

        # 方法2：检查是否有登录按钮
        login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '登录') or contains(text(), '立即登录')]")
        if login_buttons:
            for button in login_buttons:
                if button.is_displayed():
                    return False

        # 方法3：检查URL是否包含登录相关路径
        current_url = driver.current_url
        if "/signin" in current_url or "/login" in current_url:
            return False

        # 方法4：检查是否有用户头像或用户信息（表示已登录）
        try:
            user_elements = driver.find_elements(By.CSS_SELECTOR, ".user-info, .avatar, .user-avatar, [class*='user']")
            if user_elements:
                return True
        except:
            pass

        return True  # 默认认为已登录
    except Exception as e:
        print(f"⚠️ 检测登录状态失败: {e}")
        return True  # 默认认为已登录，避免误判

def send_to_wechat_group(webhook_url: str, message: str, userid: str = None):
    """发送企业微信群通知"""
    if userid:
        content = f"<@{userid}>\n{message}"
    else:
        content = message

    payload = {
        "msgtype": "markdown",
        "markdown": {
            "content": content
        }
    }

    try:
        response = requests.post(webhook_url, json=payload)
        if response.status_code == 200:
            print("✅ 企业微信通知发送成功")
        else:
            print(f"❌ 企业微信通知发送失败，状态码：{response.status_code}")
    except Exception as e:
        print(f"❌ 发送企业微信通知时出错：{e}")

def handle_login_logout(browser_id, browser_name, task_info=""):
    """处理掉登录情况"""
    print(f"🔐 检测到浏览器 {browser_id} ({browser_name}) 掉登录")

    # 发送企业微信通知
    message = (
        f"### 🔐 小红书抓取掉登录提醒\n"
        f"- **浏览器ID**: {browser_id}\n"
        f"- **浏览器名称**: {browser_name}\n"
        f"- **任务信息**: {task_info}\n"
        f"- **时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        f"- **状态**: 已暂停抓取，请手动登录后重启\n\n"
        f"请及时处理登录问题！"
    )

    send_to_wechat_group(WECHAT_WEBHOOK_URL, message)
    return False  # 返回False表示需要停止当前操作

def try_auto_login(driver, max_attempts=MAX_LOGIN_ATTEMPTS):
    """尝试自动登录 - 通过扫码或其他方式"""
    print("🔄 尝试自动登录...")

    for attempt in range(max_attempts):
        try:
            # 检查是否在登录页面
            if not check_login_status(driver):
                print(f"第 {attempt + 1} 次尝试登录...")

                # 尝试点击登录按钮
                try:
                    login_btn = driver.find_element(By.XPATH, "//button[contains(text(), '登录') or contains(text(), '立即登录')]")
                    if login_btn.is_displayed():
                        login_btn.click()
                        time.sleep(2)
                except:
                    pass

                # 等待用户扫码或手动登录
                print(f"⏳ 等待登录完成（{AUTO_LOGIN_TIMEOUT}秒超时）...")
                wait_time = 0
                while wait_time < AUTO_LOGIN_TIMEOUT:
                    time.sleep(5)
                    wait_time += 5

                    if check_login_status(driver):
                        print("✅ 登录成功！")
                        return True

                    print(f"⏳ 等待登录中... ({wait_time}/{AUTO_LOGIN_TIMEOUT}秒)")

                print(f"❌ 第 {attempt + 1} 次登录尝试超时")
            else:
                print("✅ 已经是登录状态")
                return True

        except Exception as e:
            print(f"❌ 第 {attempt + 1} 次登录尝试失败: {e}")

    print("❌ 自动登录失败，请手动登录")
    return False

def add_random_delays():
    """添加随机延迟，模拟人类行为"""
    delay = random.uniform(2, 8)
    print(f"⏱️ 随机延迟 {delay:.1f} 秒...")
    time.sleep(delay)

def safe_page_operation(driver, operation_name, operation_func, *args, **kwargs):
    """安全的页面操作，包含登录状态检查"""
    try:
        # 操作前检查登录状态
        if not check_login_status(driver):
            print(f"🔐 执行 {operation_name} 前检测到未登录")
            if not try_auto_login(driver):
                return False

        # 执行操作
        result = operation_func(*args, **kwargs)

        # 操作后再次检查登录状态
        if not check_login_status(driver):
            print(f"🔐 执行 {operation_name} 后检测到掉登录")
            if not try_auto_login(driver):
                return False

        return result

    except Exception as e:
        print(f"❌ 执行 {operation_name} 时出错: {e}")
        return False

def open_browser(window_id):
    """调用 openBrowser 方法获取 Selenium 连接信息"""
    return openBrowser(window_id)  # 直接调用已有的 openBrowser 方法

# ---------------- 获取浏览器列表 ----------------
def get_existing_browser_map():
    """获取当前所有浏览器的完整信息映射"""
    browser_map = {}
    browser_data = get_browser_list(page=0, page_size=100)

    if not browser_data or not browser_data['success']:
        print("获取浏览器列表失败，程序退出。")
        time.sleep(10)
        return browser_map

    for browser in browser_data['data']['list']:
        browser_id = browser['id']
        browser_map[browser_id] = browser

    return browser_map


# ---------------- 获取任务接口 ----------------
def fetch_task_by_action(action: str, count: int):
    """根据类型获取任务"""
    # url = "https://api.open.hctalent.cn/channel/grab/get-task"
    url = "https://api.open.hctalent.cn/channel/grab/get-task"
    payload = {
        "action": action,
        "outPutNum": count,
        "token": "maotai"
    }
    try:
        response = requests.post(url, json=payload, timeout=10)
        data = response.json()
        if data["err_no"] == 0:
            return data["results"]
        else:
            print(f"⚠️ 获取任务失败: {data.get('err_msg')}")
            return []
    except Exception as e:
        print(f"⚠️ 请求异常: {e}")
        return []


# ---------------- 单个浏览器执行任务 ----------------
def run_task_on_browser(browser_id, browser_map, task_data, task_type):
    try:
        print(f"🧪 打开浏览器: {browser_id}")
        res = open_browser(browser_id)  # 启动远程浏览器
        driver_path = res['data']['driver']
        debugger_address = res['data']['http']

        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_experimental_option("debuggerAddress", debugger_address)
        chrome_service = Service(driver_path)
        driver = webdriver.Chrome(service=chrome_service, options=chrome_options)

        # 获取浏览器名称用于通知
        browser_info = browser_map.get(browser_id, {})
        browser_name = browser_info.get('name', f'浏览器_{browser_id}')

        print(f"🔍 浏览器 {browser_id} ({browser_name}) 执行任务: {task_data['input']}")

        # 初始登录状态检查
        if not check_login_status(driver):
            print(f"🔐 浏览器 {browser_id} 初始状态未登录")
            if not try_auto_login(driver):
                handle_login_logout(browser_id, browser_name, f"任务: {task_data['input']}")
                driver.quit()
                return

        # 执行采集逻辑
        run_automation(driver, task_data, browser_id, task_type, browser_name)

        driver.quit()
    except Exception as e:
        print(f"❌ 浏览器 {browser_id} 任务出错: {e}")
        # 发送错误通知
        browser_info = browser_map.get(browser_id, {})
        browser_name = browser_info.get('name', f'浏览器_{browser_id}')
        handle_login_logout(browser_id, browser_name, f"任务执行异常: {str(e)}")


# ---------------- 并发任务调度 ----------------
def run_tasks_concurrently(browser_ids, browser_map, task_list, task_type, max_workers=5, delay=5):
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for i, browser_id in enumerate(browser_ids):
            if i >= len(task_list): break
            task_data = task_list[i]
            future = executor.submit(run_task_on_browser, browser_id, browser_map, task_data, task_type)
            futures.append(future)
            time.sleep(delay)  # 错峰启动

        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"❌ 任务线程异常: {e}")


# ---------------- 主循环 ----------------
def main_loop():
    while True:
        browser_map = get_existing_browser_map()
        browser_ids = list(browser_map.keys())
        if not browser_ids:
            print("⚠️ 没有可用浏览器，等待 30 秒")
            time.sleep(30)
            continue

        print(f"🔄 尝试获取【采集固定的词】任务")
        # tasks = fetch_task_by_action("采集固定的词", len(browser_ids))
        # task_type = "keyword"
        tasks = None
        if not tasks:
            print("📭 没有固定词任务，尝试获取【采集博主】任务")
            tasks = fetch_task_by_action("定向采集博主", len(browser_ids))
            task_type = "blogger"

        if not tasks:
            print("😴 当前无任务，等待 30 秒重试")
            time.sleep(30)
            continue

        run_tasks_concurrently(browser_ids, browser_map, tasks, task_type)

        print("🕓 所有任务执行完成，等待 10 秒进入下一轮")
        time.sleep(10)


# ---------------- 模拟执行自动化任务 ----------------
def parse_count(text):
    try:
        if "万" in text:
            return int(float(text.replace("万", "")) * 10000)
        if 'w' in text.lower():
            return int(float(text.lower().replace('w', '')) * 10000)
        return int(text)
    except:
        return 0


def parse_number(text):
    text = text.strip().replace(",", "")
    if "万" in text:
        return int(float(text.replace("万", "")) * 10000)
    try:
        return int(text)
    except:
        return 0


def is_already_grabbed(note_url, red_id):
    """根据 search_result 链接中的笔记ID和小红书号判断是否已采集"""
    try:
        # 提取 note_id
        start_flag = "/search_result/"
        if start_flag not in note_url:
            print(f"❌ 链接中不包含 {start_flag}：{note_url}")
            return False, None

        start_index = note_url.index(start_flag) + len(start_flag)
        end_index = note_url.find("?", start_index)
        note_id = note_url[start_index:] if end_index == -1 else note_url[start_index:end_index]

        if len(note_id) != 24:
            print(f"❌ 提取的 note_id 长度不对：{note_id}")
            return False, None

        payload = {
            "userId": red_id,
            "otherRecordId": note_id,
            "source": 5,
            "token": "maotai"
        }

        response = requests.post("https://api.open.hctalent.cn/channel/grab/repeat-grab-record", json=payload)
        res_json = response.json()

        if res_json.get("results", False):
            print(f"⚠️ 已采集过：{note_id}")
        else:
            print(f"✅ 未采集：{note_id}")

        return res_json.get("results", False), note_id

    except Exception as e:
        print("❌ 判断是否重复采集出错：", e)
        return False, None


def grab_and_upload_comments(driver, note_count, task_data, comments, grabTaskId, grabTaskInputId,
                              grab_record_detail_id, grab_record_detail_mingdao_id,
                              grab_user_grab_id, grab_user_mingdao_id):
    isSpiderCommentUser = task_data.get('isSpiderCommentUser', 0)
    base_url = "https://www.xiaohongshu.com" 

    try:
        if comments <= 0:
            print("❌ 评论数量设置为0，跳过抓取。")
            return

        wait = WebDriverWait(driver, 15)
        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "comments-container")))
        parent_comments = driver.find_elements(By.CSS_SELECTOR, ".comment-item")  # 这里用新的class名

        comment_list = []

        # 记录当前笔记详情页标签句柄，方便后续切回
        detail_page_handle = driver.current_window_handle

        for c in parent_comments[:comments]:
            try:
                username_elem = c.find_element(By.CSS_SELECTOR, ".author-wrapper .author a.name")
                username = username_elem.text.strip()
                content = c.find_element(By.CSS_SELECTOR, ".content .note-text span").text.strip()

                # 时间与地点
                try:
                    time_text = c.find_element(By.CSS_SELECTOR, ".info .date span:not(.location)").text.strip()
                except:
                    time_text = ""
                try:
                    place = c.find_element(By.CSS_SELECTOR, ".info .date span.location").text.strip()
                except:
                    place = ""

                # 点赞数，注意这里是文本“赞”，需要特殊处理
                try:
                    likes_elem = c.find_element(By.CSS_SELECTOR, ".info .interactions .like .count")
                    likes_text = likes_elem.text.strip()
                    # 这个“赞”可能不是数字，直接给0
                    likes = 0
                except:
                    likes = 0

                try:
                    if "前" in time_text or "刚刚" in time_text:
                        dt = datetime.datetime.now()
                    else:
                        dt = datetime.datetime.strptime(time_text, "%Y-%m-%d %H:%M")
                except:
                    dt = datetime.datetime.now()
                timestamp = int(time.mktime(dt.timetuple()))

                user_info = {
                    "userName": username,
                    "userId": "",
                    "source": 5,
                    "type": 1,
                    "typeDesc": "",
                    "info": "",
                    "tags": "",
                    "likes": 0,
                    "follow": 0,
                    "fans": 0,
                    "collect": 0,
                    "notesNum": note_count,
                    "updatedAt": ""
                }

                if isSpiderCommentUser == 1:
                    # 获取头像a标签，拼接完整url
                    avatar_link_elem = c.find_element(By.CSS_SELECTOR, ".avatar a")
                    relative_href = avatar_link_elem.get_attribute("href")
                    if not relative_href.startswith("http"):
                        profile_url = base_url + relative_href
                    else:
                        profile_url = relative_href

                    # 打开新标签页
                    driver.execute_script("window.open(arguments[0]);", profile_url)
                    # 切换到新标签页
                    driver.switch_to.window(driver.window_handles[-1])
                    try:
                        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "info-part")))

                        try:
                            red_id_elem = driver.find_element(By.CSS_SELECTOR, ".user-redId")
                            red_id = red_id_elem.text.replace("小红书号：", "").strip()
                        except:
                            red_id = ""

                        try:
                            ip_elem = driver.find_element(By.CSS_SELECTOR, ".user-IP")
                            ip_location = ip_elem.text.replace("IP属地：", "").strip()
                        except:
                            ip_location = ""

                        # 初始化默认值
                        follow = fans = likes_and_fav = 0

                        try:
                            interaction_blocks = driver.find_elements(By.CSS_SELECTOR, ".user-interactions > div")
                            for block in interaction_blocks:
                                try:
                                    label = block.find_element(By.CLASS_NAME, "shows").text.strip()
                                    count_text = block.find_element(By.CLASS_NAME, "count").text.strip()
                                    count = int(count_text) if count_text.isdigit() else 0

                                    if "关注" in label:
                                        follow = count
                                    elif "粉丝" in label:
                                        fans = count
                                    elif "获赞" in label or "收藏" in label:
                                        likes_and_fav = count
                                except:
                                    continue
                        except:
                            pass

                        # 更新字段到 user_info
                        user_info.update({
                            "userId": red_id,
                            "info": f"IP属地：{ip_location}" if ip_location else "",
                            "follow": follow,
                            "fans": fans,
                            "likes": likes_and_fav,
                        })

                        print("🔍 抓取的评论人详细信息:", user_info)

                    finally:
                        # 关闭当前评论人详情页标签
                        driver.close()
                        # 切回笔记详情页标签
                        driver.switch_to.window(detail_page_handle)

                comment_data = {
                    "grabComment": {
                        "userName": username,
                        "contents": content,
                        "place": place or "未知",
                        "time": timestamp,
                        "likes": likes
                    },
                    "grabUser": user_info
                }

                comment_list.append(comment_data)

            except Exception as e:
                # print(f"❗ 某条评论解析失败: {e}")
                continue

        print("\n💬 抓取到的评论如下：")
        for i, cm in enumerate(comment_list):
            c = cm["grabComment"]
            print(f"{i+1}. [{c['time']}] {c['userName']} ({c['place']}) {c['likes']}: {c['contents']}")

        # 上传
        comment_payload = {
            "grabTaskId": grabTaskId,
            "grabTaskInputId": grabTaskInputId,
            "grabRecordDetailId": grab_record_detail_id,
            "grabRecordDetailMingdaoId": grab_record_detail_mingdao_id,
            "grabUserGrabId": grab_user_grab_id,
            "grabUserMingdaoId": grab_user_mingdao_id,
            "grabCommentList": comment_list,
            "token": "maotai"
        }

        res = requests.post("https://api.open.hctalent.cn/channel/grab/add-comment", json=comment_payload)
        print(f"\n🚀 评论上传结果：{res.status_code} {res.text}")

    except Exception as e:
        print(f"🔥 抓取或上传评论失败：{e}")


def extract_blogger_info(driver):
    try:
        wait = WebDriverWait(driver, 10)

        # 等待 user-info 主容器出现，确保页面加载稳定
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".user-info")))

        # 昵称
        try:
            username = wait.until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, ".user-name"))).text.strip()
        except:
            username = "(未知昵称)"

        # 小红书号
        try:
            red_id = wait.until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, ".user-redId"))).text.strip().replace("小红书号：", "")
        except:
            red_id = "(未知小红书号)"

        # IP属地
        try:
            city = wait.until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, ".user-IP"))).text.strip().replace("IP属地：", "")
        except:
            city = "(未知城市)"

        # 简介
        try:
            user_desc = wait.until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, ".user-desc"))).text.strip()
        except:
            user_desc = ""

        # 标签（如果有）
        try:
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".user-tags")))
            tags = driver.find_elements(By.CSS_SELECTOR, ".user-tags .tag-item")
            tag_texts = [tag.text.strip() for tag in tags if tag.text.strip()]
            tag_str = " / ".join(tag_texts)
        except:
            tag_str = ""

        # 关注、粉丝、获赞与收藏
        try:
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".user-interactions")))
            interactions = driver.find_elements(By.CSS_SELECTOR, ".user-interactions > div")
            follows = fans = likes_collects = 0
            for item in interactions:
                label = item.find_element(By.CSS_SELECTOR, ".shows").text.strip()
                count = parse_count(item.find_element(By.CSS_SELECTOR, ".count").text.strip())
                if "关注" in label:
                    follows = count
                elif "粉丝" in label:
                    fans = count
                elif "获赞" in label:
                    likes_collects = count
        except:
            follows = fans = likes_collects = 0

        print(f"""
📌 博主信息：
👤 昵称：{username}
📇 小红书号：{red_id}
📍 IP属地：{city}
🏷️ 标签：{tag_str}
📝 简介：{user_desc}
📊 关注：{follows} | 粉丝：{fans} | 获赞与收藏：{likes_collects}
""")

        return {
            "nickname": username,
            "red_id": red_id,
            "city": city,
            "tags": tag_str,
            "desc": user_desc,
            "follows": follows,
            "fans": fans,
            "likes_collects": likes_collects,
        }

    except Exception as e:
        print("❌ 抓取博主信息失败：", e)
        return None
    

def add_grab_record(data):
    try:
        res = requests.post("https://api.open.hctalent.cn/channel/grab/add-grab-record", json=data)
        print(f"📤 上传结果：{res.status_code} {res.text}")
        if res.status_code == 200:
            return res.json().get("results", {})
    except Exception as e:
        print(f"❌ 上传失败：{e}")
    return {}


def extract_topic_word_list(driver):
    try:
        WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'a.tag'))
        )
        tag_elements = driver.find_elements(By.CSS_SELECTOR, 'a.tag')
        topic_words = [tag.text.strip().lstrip("#") for tag in tag_elements if tag.text.strip()]
        print(topic_words)
        return topic_words
    except Exception as e:
        print("提取话题词失败:", e)
        return []

def get_cover_image(driver):
    # 1. 尝试通过 <img class="note-slider-img"> 获取
    try:
        img_elem = driver.find_element(By.CLASS_NAME, "note-slider-img")
        img_src = img_elem.get_attribute("src")
        if img_src:
            return img_src
    except:
        pass

    # 2. 尝试通过 <xg-poster class="xgplayer-poster"> 的 style 获取 background-image
    try:
        poster_elem = driver.find_element(By.CLASS_NAME, "xgplayer-poster")
        style = poster_elem.get_attribute("style")
        match = re.search(r'background-image:\s*url\(["\']?(.*?)["\']?\)', style)
        if match:
            return match.group(1)
    except:
        pass

    return ""  # 没有找到任何封面图

def handle_blogger_task(driver, task_data, note_count, browser_id="", browser_name=""):

    keyword_search = task_data['input']
    driver.switch_to.window(driver.window_handles[-1])
    print("✅ 已点击进入用户主页")
    blogger_window = driver.current_window_handle

    # 进入博主页面后检查登录状态
    if not check_login_status(driver):
        print(f"🔐 进入博主页面后检测到未登录")
        if not try_auto_login(driver):
            handle_login_logout(browser_id, browser_name, f"进入博主页面时掉登录，博主: {keyword_search}")
            return

    add_random_delays()
    
    # 抓取博主信息
    # 调用函数，获取博主信息
    blogger_info = extract_blogger_info(driver)

    # 获取并打印博主主页链接
    bloggerLink = driver.current_url
    print(f"📎 博主主页链接: {bloggerLink}")

    # 判断是否成功返回
    if blogger_info:
        username = blogger_info.get("nickname", "")
        red_id = blogger_info.get("red_id", "")
        city = blogger_info.get("city", "")
        tag_str = blogger_info.get("tags", "")
        user_desc = blogger_info.get("desc", "")
        follows = blogger_info.get("follows", 0)
        fans = blogger_info.get("fans", 0)
        likes_collects = blogger_info.get("likes_collects", 0)

    note_urls = set()
    scroll_times = 0
    last_count = 0
    max_scrolls = 30
    scroll_pause = 2

    # 参数处理
    input_red_id = task_data.get('input', '')
    likes_gt = task_data.get('likesGt', 0)
    comments_gt = task_data.get('commentsGt', 0)
    max_records = task_data.get('spiderRecordNum', 100)
    record_type = task_data.get('recordType', '最热')
    record_sort = task_data.get('recordSort', '图文')
    grabTaskId = task_data.get("grabTaskId", 0)
    grabTaskInputId = task_data.get("grabTaskInputId", 0)
    comments = task_data.get('comments', 0)
    isSpiderTopicWord = task_data.get('isSpiderTopicWord', 0)

    # 获取所有笔记链接
    while scroll_times < max_scrolls:
        time.sleep(2)
        notes = driver.find_elements(By.CSS_SELECTOR, "a.cover.mask.ld")
        for note in notes:
            href = note.get_attribute("href")
            if href:
                full_url = "https://www.xiaohongshu.com" + href if href.startswith("/") else href
                note_urls.add(full_url)

        current_count = len(note_urls)
        if current_count == last_count:
            print(f"📌 已无新增笔记，停止滚动，累计笔记数：{current_count}")
            break
        else:
            print(f"📌 第{scroll_times + 1}次滚动后，笔记数增加至：{current_count}")
            last_count = current_count

        driver.execute_script("window.scrollBy(0, 1500);")
        time.sleep(scroll_pause)
        scroll_times += 1

    collected_count = 0

    # 遍历详情页
    for idx, url in enumerate(note_urls):
        # 延迟限制
        add_random_delays()

        if collected_count >= max_records:
            print(f"🚫 已达最大抓取数量：{max_records}，停止采集")
            break

        # 每处理5个笔记检查一次登录状态
        if idx % 5 == 0 and idx > 0:
            if not check_login_status(driver):
                print(f"🔐 博主笔记抓取过程中检测到掉登录 (第{idx}个笔记)")
                if not try_auto_login(driver):
                    handle_login_logout(browser_id, browser_name, f"博主笔记抓取中掉登录，已处理: {idx}")
                    break

        # 提取 note_id（取 ? 前最后一段）
        try:
            pure_url = url.split('?')[0]
            note_id = pure_url.strip('/').split('/')[-1]
        except Exception as e:
            print(f"⚠️ 无法解析 note_id，跳过此链接：{url}")
            continue

        # 去重判断
        # try:
        #     payload = {
        #         "userId": input_red_id,
        #         "otherRecordId": note_id,
        #         "source": 5,
        #         "token": "maotai"
        #     }
        #     response = requests.post("https://api.open.hctalent.cn/channel/grab/repeat-grab-record", json=payload)
        #     res_json = response.json()

        #     if res_json.get("results", False):
        #         print(f"⚠️ 已采集过：{note_id}")
        #         continue
        #     else:
        #         print(f"✅ 未采集：{note_id}")
        # except Exception as e:
        #     print(f"⚠️ 去重接口调用失败，跳过此笔记：{e}")
        #     continue

        try:
            driver.execute_script("window.open(arguments[0]);", url)
            driver.switch_to.window(driver.window_handles[-1])

            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "detail-title"))
            )

            try:
                title = driver.find_element(By.ID, "detail-title").text.strip()
            except:
                title = "(无标题)"

            try:
                content = driver.find_element(By.ID, "detail-desc").text.strip()
            except:
                content = "(无正文)"

            # 话题词
            topic_words = []
            if (isSpiderTopicWord == 1):   
                topic_words = extract_topic_word_list(driver)

            try:
                interactions = driver.find_element(By.CSS_SELECTOR, "div.interactions.engage-bar")
                like_count_text = interactions.find_element(By.CSS_SELECTOR, ".like-wrapper .count").text.strip()
                like_count = parse_count(like_count_text)
            except:
                like_count = 0

            try:
                comment_count_text = driver.find_element(By.CSS_SELECTOR, ".chat-wrapper .count").text.strip()
                comment_count = parse_count(comment_count_text)
            except:
                comment_count = 0

            try:
                collect_count_text = driver.find_element(By.CSS_SELECTOR, ".collect-wrapper .count").text.strip()
                collect_count = parse_count(collect_count_text)
            except:
                collect_count = 0

            if like_count < likes_gt:
                print(f"🚫 第{idx+1}条点赞数({like_count})不足 {likes_gt}，跳过")
                continue

            if comment_count < comments_gt:
                print(f"🚫 第{idx+1}条评论数({comment_count})不足 {comments_gt}，跳过")
                continue

            collected_count += 1

            print(f"""
✅ 第{collected_count} 条笔记详情：
标题：{title}
内容预览：{content[:100]}...
👍 点赞数：{like_count}    💬 评论数：{comment_count}    📥 收藏数：{collect_count}
🔗 链接：{url}
""")
            
            # ✅ 获取封面图 src（在 class="note-slider-img" 的 <img> 标签中）
            img_src = get_cover_image(driver)
            print("封面图链接：", img_src)
            updated_time = int(time.time())

            upload_data = {
                "grabTaskId": grabTaskId,
                "grabTaskInputId": grabTaskInputId,
                "spiderRoundFlag": task_data.get("spiderRoundFlag", f"spider2-{time.strftime('%Y-%m-%d+%H:%M:%S')}"),
                "source": 5,
                "searchWord": keyword_search,
                "title": title,
                "type": 0,
                "userName": username,
                "coverPic": img_src,  # 封面图链接
                "contents": content,
                "likes": like_count,
                "collect": collect_count,
                "comment": comment_count,
                "url": url,
                "inCity": city,
                "otherRecordId": note_id,
                "topicWordList": topic_words or [],  # 话题词
                "updated": updated_time,
                "grabUser": {
                    "source": 5,
                    "userId": red_id,
                    "userName": username,
                    "type": 1,
                    "typeDesc": "类型备注",
                    "info": "",
                    "tags": "",
                    "fans": fans,
                    "follow": follows,
                    "likes": likes_collects,
                    "collect": collect_count,
                    "notesNum": note_count,
                    "bloggerLink": bloggerLink
                },
                "token": "maotai"
            }

            # 上传记录
            record_result = add_grab_record(upload_data)

            # 提取返回值中的 grabId / mingdaoId 等调用添加评论接口
            if comment_count > 0 and comments > 0 :
                grab_record_detail_id = record_result.get("grabId")
                grab_record_detail_mingdao_id = record_result.get("mingdaoId")
                grab_user_grab_id = record_result.get("grabUserGrabId")
                grab_user_mingdao_id = record_result.get("grabUserMingdaoId")
                grab_and_upload_comments(                
                    driver,
                    note_count,
                    task_data,
                    comments,
                    grabTaskId,
                    grabTaskInputId,
                    grab_record_detail_id,
                    grab_record_detail_mingdao_id,
                    grab_user_grab_id,
                    grab_user_mingdao_id)

        except Exception as e:
            print(f"⚠️ 第{idx + 1}条笔记抓取失败: {e}")

        finally:
            if len(driver.window_handles) > 1:
                driver.close()
                driver.switch_to.window(blogger_window)
    
    # 所有笔记处理完毕后，更新任务状态
    try:
        update_payload = {
            "grabTaskId": grabTaskId,
            "grabTaskInputId": grabTaskInputId,
            "status": 2,
            "token": "maotai"
        }

        response = requests.post("https://api.open.hctalent.cn/channel/grab/update-task", json=update_payload, timeout=10)
        if response.status_code == 200:
            print("✅ 抓取任务状态已更新")
        else:
            print(f"⚠️ 抓取任务状态更新失败，状态码：{response.status_code}, 响应内容：{response.text}")
    except Exception as e:
        print(f"❌ 调用 grab/update-task 接口失败：{e}")


    

def run_automation(driver, task_data, browser_id, task_type, browser_name=""):
    keyword = task_data['input']
    record_type = task_data.get('recordType', '最热')
    record_sort = task_data.get('recordSort', '图文')
    likes_gt = task_data.get('likesGt', 0)
    comments_gt = task_data.get('commentsGt', 0)
    max_records = task_data.get('spiderRecordNum', 100)
    comments = task_data.get('comments', 0)

    print(f"🌐 浏览器 {browser_id} 抓取关键词: {keyword}")

    # 访问主页前检查登录状态
    driver.get("https://www.xiaohongshu.com/explore")
    add_random_delays()

    # 访问页面后检查登录状态
    if not check_login_status(driver):
        print(f"🔐 访问主页后检测到浏览器 {browser_id} 未登录")
        if not try_auto_login(driver):
            handle_login_logout(browser_id, browser_name, f"访问主页时掉登录，任务: {keyword}")
            return []

    # 记录 explore 页面的主窗口句柄
    main_window = driver.current_window_handle

    # 搜索关键词
    try:
        input_box = driver.find_element(By.ID, "search-input")
        input_box.clear()
        input_box.send_keys(keyword)
        input_box.send_keys(Keys.ENTER)
        print(f"🔍 已搜索关键词：{keyword}")
    except Exception as e:
        print(f"❌ 搜索失败：{e}")
        return []

    add_random_delays()

    # 搜索后检查登录状态
    if not check_login_status(driver):
        print(f"🔐 搜索后检测到浏览器 {browser_id} 未登录")
        if not try_auto_login(driver):
            handle_login_logout(browser_id, browser_name, f"搜索时掉登录，关键词: {keyword}")
            return []

    # 判断是否抓取博主
    if task_type == "blogger":
        # 点击"用户"Tab前检查登录状态
        if not check_login_status(driver):
            print(f"🔐 点击用户Tab前检测到浏览器 {browser_id} 未登录")
            if not try_auto_login(driver):
                handle_login_logout(browser_id, browser_name, f"点击用户Tab前掉登录，关键词: {keyword}")
                return []

        # 点击“用户”Tab
        user_tab = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, '//div[@id="user" and contains(@class, "channel")]'))
        )
        user_tab.click()
        print("✅ 已点击用户tab")

        add_random_delays()

        # 等待并获取第一个笔记数 span 元素
        try:
            note_span = WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.XPATH, "//span[contains(text(), '笔记・')]"))
            )
            note_text = note_span.text
            note_count = int(note_text.split('・')[1])
        except Exception:
            note_count = 0

        # 进入第一个用户
        user_card = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, ".user-item-box"))
        )
        user_card.click()
        handle_blogger_task(driver, task_data, note_count, browser_id, browser_name)

        # 清理新标签页并切回主窗口
        if len(driver.window_handles) > 1:
            for handle in driver.window_handles:
                if handle != main_window:
                    driver.switch_to.window(handle)
                    driver.close()
            driver.switch_to.window(main_window)   

        # 延迟限制
        time.sleep(random.uniform(5, 30))

    elif task_type == "keyword":
        # 筛选“图文”或“视频”
        try:
            if record_sort == "图文":
                driver.find_element(By.ID, "image").click()
            elif record_sort == "视频":
                driver.find_element(By.ID, "video").click()
            print(f"✅ 已筛选内容类型：{record_sort}")
        except:
            print(f"⚠️ 无法筛选内容类型：{record_sort}")

        time.sleep(1)

        # 筛选“最热”等排序
        try:
            # 点击下拉菜单按钮（展开选项）
            driver.find_element(By.CSS_SELECTOR, "div.filter").click()
            time.sleep(1)

            # 找到所有 dropdown-container
            dropdowns = driver.find_elements(By.CLASS_NAME, "dropdown-container")

            # 遍历 dropdowns，找到那个显示为 inline-block 的（也就是展开的那个）
            visible_dropdown = None
            for dropdown in dropdowns:
                style = dropdown.find_element(By.CLASS_NAME, "dropdown-items").get_attribute("style")
                if "inline-block" in style and "visible" in style:
                    visible_dropdown = dropdown
                    break

            # 在 visible_dropdown 中点击“最热”
            if visible_dropdown:
                options = visible_dropdown.find_elements(By.CSS_SELECTOR, "li span.text")
                for option in options:
                    if record_type in option.text:
                        option.click()
                        # print("已点击：{record_type}")
                        break
            else:
                print("没有找到展开的 dropdown")

        except Exception as e:
            print(f"⚠️ 无法筛选排序方式：{record_type}, 错误：{e}")

        time.sleep(2)

        record_count = 0
        seen_urls = set()
        scroll_times = 0

        while record_count < max_records and scroll_times < 60:
            # 每LOGIN_CHECK_INTERVAL次滚动检查一次登录状态
            if scroll_times % LOGIN_CHECK_INTERVAL == 0 and scroll_times > 0:
                if not check_login_status(driver):
                    print(f"🔐 抓取过程中检测到浏览器 {browser_id} 掉登录 (第{scroll_times}次滚动)")
                    if not try_auto_login(driver):
                        handle_login_logout(browser_id, browser_name, f"抓取过程中掉登录，关键词: {keyword}, 已抓取: {record_count}")
                        break

            try:
                posts = driver.find_elements(By.CSS_SELECTOR, "section.note-item")
                print(f"📄 当前列表中检测到帖子数：{len(posts)}")
            except Exception as e:
                print(f"⚠️ 加载帖子列表失败：{e}")
                break

            for post in posts:
                try:
                    like_element = WebDriverWait(post, 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".like-wrapper .count"))
                    )
                    like_count = parse_count(like_element.text.strip())
                    if like_count < likes_gt:
                        continue

                    # 获取 URL 并判断是否已采集
                    link = post.find_element(By.CSS_SELECTOR, "a.cover")
                    url = link.get_attribute("href")
                    if url in seen_urls:
                        continue
                    seen_urls.add(url)

                    # 打开详情页
                    driver.execute_script("window.open(arguments[0]);", url)
                    driver.switch_to.window(driver.window_handles[-1])
                    add_random_delays()

                    # 打开详情页后检查登录状态
                    if not check_login_status(driver):
                        print(f"🔐 打开详情页后检测到浏览器 {browser_id} 未登录")
                        driver.close()
                        driver.switch_to.window(driver.window_handles[0])
                        if not try_auto_login(driver):
                            handle_login_logout(browser_id, browser_name, f"打开详情页时掉登录，URL: {url}")
                            break
                        continue

                    # 获取评论数
                    try:
                        comment_element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".chat-wrapper .count"))
                        )
                        comment_count = parse_count(comment_element.text.strip())
                    except Exception as e:
                        comment_count = 0
                        print(f"⚠️ 获取评论失败：{e}")

                    if comment_count < comments_gt:
                        print(f"🛑 评论数不达标（{comment_count} < {comments_gt}），跳过")
                        driver.close()
                        driver.switch_to.window(driver.window_handles[0])
                        continue

                    # 获取标题
                    try:
                        title = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.ID, "detail-title"))
                        ).text.strip()
                    except:
                        title = "(无标题)"

                    # 获取正文
                    try:
                        content = driver.find_element(By.ID, "detail-desc").text.strip()
                    except:
                        content = "(无正文)"

                    # 获取收藏数
                    try:
                        collect_element = driver.find_element(By.CSS_SELECTOR, ".collect-wrapper .count")
                        collect_count = parse_count(collect_element.text.strip())
                    except Exception as e:
                        collect_count = 0
                        print(f"⚠️ 获取收藏数失败：{e}")

                    # 抓取评论
                    if comment_count > 0 and comments > 0 :
                        wait = WebDriverWait(driver, 15)
                        # 等待评论容器加载
                        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "comments-container")))
                        # 找到所有一级评论容器
                        parent_comments = driver.find_elements(By.CSS_SELECTOR, ".parent-comment")

                        comment_list  = []
                        for c in parent_comments[:comments]:  # 只取前comments条评论
                            try:
                                username = c.find_element(By.CSS_SELECTOR, ".author > a.name").text.strip()
                                content = c.find_element(By.CSS_SELECTOR, ".content > span.note-text").text.strip()
                                time_text = c.find_element(By.CSS_SELECTOR, ".info > .date > span").text.strip()
                                comment_list.append({
                                    "username": username,
                                    "content": content,
                                    "time": time_text
                                })
                            except Exception as e:
                                print(f"某条评论解析失败: {e}")
                                continue

                        # 打印抓取结果
                        for i, cm in enumerate(comment_list):
                            print(f"{i+1}. [{cm['time']}] {cm['username']}: {cm['content']}")


                    # 进入用户页
                    try:
                        # 获取用户名和链接
                        user_link_elem = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".author-wrapper .info a.name"))
                        )
                        username = user_link_elem.text.strip()
                        profile_url = user_link_elem.get_attribute("href")

                        # 进入用户主页
                        driver.get(profile_url)
                        add_random_delays()

                        # 访问用户主页后检查登录状态
                        if not check_login_status(driver):
                            print(f"🔐 访问用户主页后检测到浏览器 {browser_id} 未登录")
                            driver.close()
                            driver.switch_to.window(driver.window_handles[0])
                            if not try_auto_login(driver):
                                handle_login_logout(browser_id, browser_name, f"访问用户主页时掉登录，用户: {username}")
                                break
                            continue

                        # 获取城市
                        try:
                            city_span = driver.find_element(By.CSS_SELECTOR, "span.user-IP")
                            city_text = city_span.text.strip()
                            # 假设格式是 "IP属地：广东"，直接用冒号分割取后半部分
                            city = city_text.split("：")[-1] if "IP属地：" in city_text else "(未知城市)"
                        except:
                            city = "(未知城市)"

                        # 获取小红书号
                        try:
                            # 等待包含“小红书号”的 span 元素出现
                            red_id_elem = WebDriverWait(driver, 5).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".user-redId"))
                            )
                            red_id_text = red_id_elem.text.strip()  # 例如 "小红书号：11804099236"
                            red_id = red_id_text.replace("小红书号：", "").strip()

                            # print("✅ 小红书号获取成功：", red_id)

                        except Exception as e:
                            print("❌ 获取小红书号失败：", e)

                        # 3. 判断是否重复抓取（前提是拿到了 red_id）
                        if red_id:
                            is_grabbed, note_id = is_already_grabbed(url, red_id)
                            if is_grabbed:
                                print(f"⚠️ 笔记 {url} 已采集过，跳过")
                                # 关闭详情页
                                driver.close()
                                driver.switch_to.window(driver.window_handles[0])
                                continue

                        # 获取关注、粉丝、获赞
                        try:
                            counts = WebDriverWait(driver, 10).until(
                                EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".user-interactions .count"))
                            )
                            follows = parse_number(counts[0].text) if len(counts) > 0 else 0
                            fans = parse_number(counts[1].text) if len(counts) > 1 else 0
                            likes_collects = parse_number(counts[2].text) if len(counts) > 2 else 0
                        except Exception as e:
                            print("⚠️ 获取互动数据失败：", e)
                            follows = fans = likes_collects = 0

                        # 返回笔记页
                        # driver.back()
                        time.sleep(2)

                    except Exception as e:
                        print(f"⚠️ 获取用户信息失败")
                        username = "(未知用户)"
                        red_id = "(未知小红书号)"
                        follows = fans = likes_collects = 0

                    # 保存数据
                    print(f"""
                    📌 笔记：[{like_count}赞 / {comment_count}评 / {collect_count}藏]/笔记标题： {title}
                    🌍 城市：{city}
                    👤 用户：{username}（小红书号：{red_id}） | 关注：{follows} | 粉丝：{fans} | 获赞与收藏：{likes_collects}
                    🔗 链接：{url}
                    """)    

                    # 当前时间戳（秒）
                    updated_time = int(time.time())

                    upload_data = {
                        "grabTaskId": task_data.get("grabTaskId", 0),
                        "grabTaskInputId": task_data.get("grabTaskInputId", 0),
                        "spiderRoundFlag": task_data.get("spiderRoundFlag", f"spider2-{time.strftime('%Y-%m-%d+%H:%M:%S')}"),
                        "source": 5,
                        "searchWord": keyword,
                        "title": title,
                        "type": 0,
                        "userName": username,
                        "coverPic": "",  # 封面图 URL 可替换
                        "contents": content,
                        "likes": like_count,
                        "collect": collect_count,
                        "comment": comment_count,
                        "url": url,
                        "inCity": city,
                        "otherRecordId": note_id,  # 笔记ID
                        "topicWordList": [],  # 提取话题词可填入
                        "updated": updated_time,
                        "grabUser": {
                            "source": 5,
                            "userId": red_id,
                            "userName": username,
                            "type": 1,
                            "typeDesc": "类型备注",
                            "info": "",  # 用户简介信息能抓取可填
                            "tags": "",  # 提取用户标签
                            "fans": fans,
                            "follow": follows,
                            "likes": likes_collects,
                            "collect": collect_count,
                            "notesNum": note_count  # 统计用户发帖数可填
                        },
                        "token": "maotai"
                    }

                    upload_grab_record(upload_data)

                    record_count += 1

                    # 关闭详情页
                    driver.close()
                    driver.switch_to.window(driver.window_handles[0])

                    if record_count >= max_records:
                        break

                except Exception as e:
                    # print(f"⚠️ 采集失败：{e}")
                    continue

            # 滚动加载
            try:
                driver.execute_script("window.scrollBy(0, 1500);")
            except:
                pass
            time.sleep(2)
            scroll_times += 1

        print(f"✅ 共采集到 {record_count} 条合格笔记")

        # 调用更新任务状态接口
        try:
            update_payload = {
                "grabTaskId": task_data.get("grabTaskId", 0),
                "grabTaskInputId": task_data.get("grabTaskInputId", 0),
                "status":2,
                "token":"maotai"
            }

            response = requests.post("https://api.open.hctalent.cn/channel/grab/update-task", json=update_payload, timeout=10)
            if response.status_code == 200:
                print("✅ 抓取任务状态已更新")
            else:
                print(f"⚠️ 抓取任务状态更新失败，状态码：{response.status_code}, 响应内容：{response.text}")
        except Exception as e:
            print(f"❌ 调用 grab/update-task 接口失败：{e}")

        # 清理新标签页并切回主窗口
        if len(driver.window_handles) > 1:
            for handle in driver.window_handles:
                if handle != main_window:
                    driver.switch_to.window(handle)
                    driver.close()
            driver.switch_to.window(main_window)    

        return record_count


def upload_grab_record(data):
    try:
        headers = {
            "Authorization": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI4OWEzYzg0ZWRiNmI0M2RhYmJlMjc1YmY1Njk4MzkwYyIsInVzZXIiOiIxNzMyMTA3MzczOSIsInN1YiI6IjE3MzIxMDczNzM5In0.-n9FSHExa5P1GzlAx4zRzOiStWUcQsSDsz4wtcx6-E7v7d16tHoELrzrOQjNVkDFG3gmqhJgE5MKvw4-vIphxw"
        }

        resp = requests.post("https://api.open.hctalent.cn/channel/grab/add-grab-record", json=data, headers=headers)
        resp_data = resp.json()
        print("📦 接口返回数据：", resp_data)

        if resp_data.get("err_no") == 0:
            print(f"✅ 笔记上传成功：{data.get('title', '无标题')}")
        else:
            print(f"❌ 笔记上传失败：{resp_data.get('err_msg')}")
    except Exception as e:
        print(f"❌ 上传接口调用异常：{e}")


def print_login_protection_info():
    """打印登录保护功能说明"""
    print("=" * 60)
    print("🔐 小红书抓取登录保护功能已启用")
    print("=" * 60)
    print("✅ 新增功能:")
    print("  • 自动检测登录状态")
    print("  • 掉登录时自动尝试重新登录")
    print("  • 企业微信异常通知")
    print("  • 智能延迟，模拟人类行为")
    print("  • 多点登录状态检查")
    print()
    print("🔧 配置参数:")
    print(f"  • 登录检查间隔: 每 {LOGIN_CHECK_INTERVAL} 次操作")
    print(f"  • 自动登录超时: {AUTO_LOGIN_TIMEOUT} 秒")
    print(f"  • 最大登录尝试: {MAX_LOGIN_ATTEMPTS} 次")
    print()
    print("📍 检查点:")
    print("  • 访问主页后")
    print("  • 搜索关键词后")
    print("  • 打开详情页后")
    print("  • 访问用户主页后")
    print("  • 抓取过程中定期检查")
    print("=" * 60)

if __name__ == "__main__":
    print_login_protection_info()
    main_loop()
